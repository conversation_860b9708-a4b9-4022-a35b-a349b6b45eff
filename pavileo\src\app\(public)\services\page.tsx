import { Metadata } from 'next';
import Image from 'next/image';
import { FaHome, FaBuilding, FaPaintRoller, FaRulerCombined, FaClipboardCheck, FaTools, FaLeaf, FaHardHat } from 'react-icons/fa';

export const metadata: Metadata = {
  title: 'Our Services',
  description: 'Explore the comprehensive construction services offered by Pavileo Construction Company in Varanasi.',
};

const services = [
  {
    id: 1,
    title: 'Residential Construction',
    description: 'Custom homes built to your specifications with quality materials and expert craftsmanship.',
    icon: <FaHome size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Custom home design and construction',
      'Luxury villas and bungalows',
      'Apartment buildings and complexes',
      'Affordable housing solutions',
      'Turnkey residential projects'
    ]
  },
  {
    id: 2,
    title: 'Commercial Construction',
    description: 'Functional and attractive commercial spaces designed to meet your business needs.',
    icon: <FaBuilding size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Office buildings and corporate headquarters',
      'Retail spaces and shopping centers',
      'Hotels and hospitality projects',
      'Educational institutions',
      'Healthcare facilities'
    ]
  },
  {
    id: 3,
    title: 'Renovation & Remodeling',
    description: 'Transform your existing space with our comprehensive renovation services.',
    icon: <FaPaintRoller size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Complete home renovations',
      'Kitchen and bathroom remodeling',
      'Commercial space renovations',
      'Historic building restoration',
      'Structural repairs and upgrades'
    ]
  },
  {
    id: 4,
    title: 'Interior Design',
    description: 'Professional interior design services to create beautiful and functional spaces.',
    icon: <FaRulerCombined size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Residential interior design',
      'Commercial interior design',
      'Space planning and optimization',
      'Custom furniture design',
      'Material and finish selection'
    ]
  },
  {
    id: 5,
    title: 'Project Management',
    description: 'End-to-end project management ensuring timely completion within budget.',
    icon: <FaClipboardCheck size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1507537297725-24a1c029d3ca?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Comprehensive project planning',
      'Budget development and management',
      'Schedule development and tracking',
      'Quality control and assurance',
      'Risk management and mitigation'
    ]
  },
  {
    id: 6,
    title: 'Maintenance & Repairs',
    description: 'Keep your property in top condition with our maintenance and repair services.',
    icon: <FaTools size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Preventive maintenance programs',
      'Emergency repair services',
      'Structural repairs',
      'Electrical and plumbing maintenance',
      'Seasonal property inspections'
    ]
  },
  {
    id: 7,
    title: 'Sustainable Building',
    description: 'Environmentally friendly construction practices for a greener future.',
    icon: <FaLeaf size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1510798831971-661eb04b3739?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Energy-efficient building design',
      'Solar power integration',
      'Rainwater harvesting systems',
      'Sustainable material selection',
      'Green building certification assistance'
    ]
  },
  {
    id: 8,
    title: 'Consultation Services',
    description: 'Expert advice and guidance for your construction projects.',
    icon: <FaHardHat size={40} className="text-amber-600" />,
    image: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Feasibility studies',
      'Cost estimation and budgeting',
      'Site selection and evaluation',
      'Regulatory compliance assistance',
      'Construction method recommendations'
    ]
  }
];

export default function ServicesPage() {
  return (
    <main className="pt-20">
      {/* Hero Section */}
      <section className="bg-gray-100 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Our <span className="text-amber-600">Services</span>
            </h1>
            <p className="text-lg text-gray-700 mb-8">
              We offer a comprehensive range of construction services to meet all your building needs, from residential and commercial construction to renovation and interior design.
            </p>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {services.slice(0, 4).map((service) => (
              <a key={service.id} href={`#service-${service.id}`} className="block">
                <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                  <div className="mb-6">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-4">{service.title}</h3>
                  <p className="text-gray-700">{service.description}</p>
                </div>
              </a>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.slice(4).map((service) => (
              <a key={service.id} href={`#service-${service.id}`} className="block">
                <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                  <div className="mb-6">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-4">{service.title}</h3>
                  <p className="text-gray-700">{service.description}</p>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Services */}
      {services.map((service, index) => (
        <section
          key={service.id}
          className={`py-16 md:py-24 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
          id={`service-${service.id}`}
        >
          <div className="container mx-auto px-4 md:px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className={`${index % 2 === 0 ? 'order-1' : 'order-1 lg:order-2'}`}>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">{service.title}</h2>
                <p className="text-gray-700 mb-8">{service.description}</p>

                <h3 className="text-xl font-bold mb-4">What We Offer:</h3>
                <ul className="space-y-3 mb-8">
                  {service.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-amber-600 mr-3">•</span>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className={`relative h-[400px] rounded-lg overflow-hidden ${index % 2 === 0 ? 'order-2' : 'order-2 lg:order-1'}`}>
                <Image
                  src={service.image}
                  alt={service.title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>
      ))}

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-amber-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Your Project?
            </h2>
            <p className="text-lg text-gray-700 mb-8">
              Contact us today for a free consultation and let us help you bring your vision to life.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700"
            >
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </main>
  );
}
