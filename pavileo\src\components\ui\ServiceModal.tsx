'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import OptimizedImage from './OptimizedImage';
import DetailModal from './DetailModal';
import { FaHome, FaBuilding, FaPaintRoller, FaRulerCombined, FaClipboardCheck, FaTimes } from 'react-icons/fa';

interface Service {
  _id: string;
  title: string;
  description: string;
  icon: string;
  image?: string;
  features?: string[];
  detailedDescription?: string;
}

interface ServiceModalProps {
  service: Service | null;
  isOpen: boolean;
  onClose: () => void;
}

const iconMap: Record<string, React.ReactNode> = {
  home: <FaHome size={48} className="text-amber-600" />,
  building: <FaBuilding size={48} className="text-amber-600" />,
  paintRoller: <FaPaintRoller size={48} className="text-amber-600" />,
  rulerCombined: <FaRulerCombined size={48} className="text-amber-600" />,
  clipboardCheck: <FaClipboardCheck size={48} className="text-amber-600" />,
};

const ServiceModal: React.FC<ServiceModalProps> = ({ service, isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [detailModalType, setDetailModalType] = useState<'learn-more' | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  useEffect(() => {
    if (isOpen && modalRef.current && overlayRef.current && contentRef.current) {
      // Animate modal opening
      gsap.set(modalRef.current, { display: 'flex' });
      gsap.fromTo(overlayRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.3, ease: "power2.out" }
      );
      gsap.fromTo(contentRef.current,
        { opacity: 0, scale: 0.8, y: 50 },
        { opacity: 1, scale: 1, y: 0, duration: 0.4, ease: "back.out(1.7)", delay: 0.1 }
      );

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else if (!isOpen && modalRef.current && overlayRef.current && contentRef.current) {
      // Animate modal closing
      gsap.to(contentRef.current, {
        opacity: 0,
        scale: 0.8,
        y: 50,
        duration: 0.3,
        ease: "power2.in"
      });
      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          if (modalRef.current) {
            gsap.set(modalRef.current, { display: 'none' });
          }
        }
      });

      // Restore body scroll
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!service) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleLearnMore = () => {
    setDetailModalType('learn-more');
    setIsDetailModalOpen(true);
  };

  const handleDetailModalClose = () => {
    setIsDetailModalOpen(false);
    setDetailModalType(null);
  };

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{ display: 'none' }}
    >
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={handleOverlayClick}
      />

      {/* Modal Content */}
      <div
        ref={contentRef}
        className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors duration-200"
        >
          <FaTimes className="text-gray-600" />
        </button>

        {/* Modal Body */}
        <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
          {/* Image Section */}
          <div className="relative h-64 lg:h-full">
            <OptimizedImage
              src={service.image || 'https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'}
              alt={service.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

            {/* Icon */}
            <div className="absolute bottom-6 left-6">
              <div className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                {iconMap[service.icon] || <FaHome size={48} className="text-amber-600" />}
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8 overflow-y-auto">
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {service.title}
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  {service.detailedDescription || service.description}
                </p>
              </div>

              {service.features && service.features.length > 0 && (
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    What We Offer:
                  </h3>
                  <ul className="space-y-3">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-amber-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="pt-6 border-t border-gray-200">
                <div className="flex justify-center">
                  <button
                    onClick={handleLearnMore}
                    className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
                  >
                    Learn More
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detail Modal */}
      {detailModalType && (
        <DetailModal
          isOpen={isDetailModalOpen}
          onClose={handleDetailModalClose}
          type={detailModalType}
          service={service}
        />
      )}
    </div>
  );
};

export default ServiceModal;
