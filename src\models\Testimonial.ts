import mongoose, { Schema, models } from 'mongoose';

const TestimonialSchema = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
    },
    position: {
      type: String,
    },
    content: {
      type: String,
      required: [true, 'Content is required'],
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      default: 5,
    },
    image: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

export default models.Testimonial || mongoose.model('Testimonial', TestimonialSchema);
