'use client';

import { useState, useEffect } from 'react';
import { FaBuilding, FaTools, FaComments, FaEnvelope } from 'react-icons/fa';
import DashboardCard from '@/components/admin/DashboardCard';

interface DashboardData {
  projectsCount: number;
  servicesCount: number;
  testimonialsCount: number;
  messagesCount: number;
}

export default function AdminDashboard() {
  const [data, setData] = useState<DashboardData>({
    projectsCount: 0,
    servicesCount: 0,
    testimonialsCount: 0,
    messagesCount: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        setData({
          projectsCount: 12,
          servicesCount: 5,
          testimonialsCount: 8,
          messagesCount: 3,
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <DashboardCard
              title="Projects"
              count={data.projectsCount}
              icon={<FaBuilding />}
              href="/admin/projects"
              color="border-blue-500"
            />
            <DashboardCard
              title="Services"
              count={data.servicesCount}
              icon={<FaTools />}
              href="/admin/services"
              color="border-green-500"
            />
            <DashboardCard
              title="Testimonials"
              count={data.testimonialsCount}
              icon={<FaComments />}
              href="/admin/testimonials"
              color="border-amber-500"
            />
            <DashboardCard
              title="Messages"
              count={data.messagesCount}
              icon={<FaEnvelope />}
              href="/admin/messages"
              color="border-red-500"
            />
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4">Recent Activity</h2>
            <div className="border-t border-gray-200 pt-4">
              <p className="text-gray-600">No recent activity to display.</p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
