'use client';

import { useState, useEffect, useRef } from 'react';
import Button from '@/components/ui/Button';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { animateCounter } from '@/utils/gsapAnimations';
import { companyImages } from '@/utils/imageUtils';

interface AboutData {
  title: string;
  subtitle: string;
  description: string;
  image: string;
  stats: {
    label: string;
    value: string;
  }[];
  ctaText: string;
  ctaLink: string;
}

const defaultAboutData: AboutData = {
  title: "Why Choose Pavileo",
  subtitle: "Excellence in Every Project Since 1995",
  description: "We are Varanasi's premier construction company with over 25 years of expertise in creating exceptional spaces. From luxury residences to cutting-edge commercial buildings, we combine traditional craftsmanship with innovative technology to deliver projects that exceed expectations.",
  image: "https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
  stats: [
    { label: "Projects Completed", value: "500+" },
    { label: "Years of Experience", value: "25+" },
    { label: "Happy Clients", value: "1000+" },
    { label: "Team Members", value: "50+" }
  ],
  ctaText: "Discover Our Story",
  ctaLink: "/about"
};

const AboutSection = () => {
  const [aboutData, setAboutData] = useState<AboutData>(defaultAboutData);
  const [loading, setLoading] = useState(true);

  // Refs for GSAP animations
  const sectionRef = useRef<HTMLElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        // In a real app, you would fetch this from an API
        setAboutData(defaultAboutData);
      } catch (error) {
        console.error('Error fetching about data:', error);
        setAboutData(defaultAboutData);
      } finally {
        setLoading(false);
      }
    };

    fetchAboutData();
  }, []);

  // GSAP animations
  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }

    if (!loading && imageRef.current && contentRef.current && statsRef.current) {
      // Create main timeline
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });

      // Animate image with 3D effect
      tl.fromTo(imageRef.current,
        { x: -100, opacity: 0, rotationY: -15 },
        { x: 0, opacity: 1, rotationY: 0, duration: 1, ease: "power2.out" }
      )
      // Animate content from right
      .fromTo(contentRef.current.children,
        { x: 100, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: "power2.out" },
        "-=0.5"
      );

      // Animate stats with counter effect
      const statCards = statsRef.current.querySelectorAll('.stat-card');
      statCards.forEach((card, index) => {
        const valueElement = card.querySelector('.stat-value');
        const value = valueElement?.textContent?.replace(/[^0-9]/g, '') || '0';

        gsap.fromTo(card,
          { y: 50, opacity: 0, scale: 0.8 },
          {
            y: 0,
            opacity: 1,
            scale: 1,
            duration: 0.6,
            delay: index * 0.1,
            ease: "back.out(1.7)",
            scrollTrigger: {
              trigger: statsRef.current,
              start: "top 80%",
              toggleActions: "play none none reverse"
            }
          }
        );

        // Animate counter
        if (valueElement) {
          const obj = { value: 0 };
          gsap.to(obj, {
            value: parseInt(value),
            duration: 2,
            delay: 0.5 + index * 0.1,
            ease: "power2.out",
            onUpdate: () => {
              valueElement.textContent = Math.round(obj.value) + (aboutData.stats[index]?.value.includes('+') ? '+' : '');
            },
            scrollTrigger: {
              trigger: statsRef.current,
              start: "top 80%",
              toggleActions: "play none none reverse"
            }
          });
        }
      });

      // Add hover effects to stat cards
      statCards.forEach((card) => {
        const cardElement = card as HTMLElement;

        cardElement.addEventListener('mouseenter', () => {
          gsap.to(cardElement, {
            y: -10,
            scale: 1.05,
            boxShadow: "0 15px 30px rgba(0,0,0,0.15)",
            duration: 0.3,
            ease: "power2.out"
          });
        });

        cardElement.addEventListener('mouseleave', () => {
          gsap.to(cardElement, {
            y: 0,
            scale: 1,
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
            duration: 0.3,
            ease: "power2.out"
          });
        });
      });
    }
  }, [loading, aboutData]);
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-amber-50/30">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-amber-100 rounded-full mb-6">
            <span className="text-amber-700 text-sm font-medium">🏆 Award-Winning Construction Company</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            {aboutData.title.split(' ').map((word, index) =>
              index === aboutData.title.split(' ').length - 1 ? (
                <span key={index} className="text-amber-600">{word}</span>
              ) : (
                <span key={index}>{word} </span>
              )
            )}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {aboutData.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div ref={contentRef} className="space-y-8">
            <div className="space-y-6">
              <p className="text-lg text-gray-700 leading-relaxed">
                {aboutData.description}
              </p>

              {/* Features */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Quality Assurance</h4>
                    <p className="text-gray-600">Premium materials and expert craftsmanship</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">On-Time Delivery</h4>
                    <p className="text-gray-600">Projects completed within scheduled timelines</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Expert Team</h4>
                    <p className="text-gray-600">Skilled professionals with decades of experience</p>
                  </div>
                </div>
              </div>
            </div>

            <Button
              href={aboutData.ctaLink}
              className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              {aboutData.ctaText}
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Button>
          </div>

          {/* Image Grid */}
          <div ref={imageRef} className="space-y-6">
            {/* Main Image */}
            <div className="relative h-[400px] rounded-2xl overflow-hidden shadow-2xl">
              <OptimizedImage
                src={aboutData.image}
                alt={aboutData.title}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>

            {/* Secondary Images */}
            <div className="grid grid-cols-2 gap-4">
              <div className="relative h-[180px] rounded-xl overflow-hidden shadow-lg">
                <OptimizedImage
                  src="https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                  alt="Construction Team"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="relative h-[180px] rounded-xl overflow-hidden shadow-lg">
                <OptimizedImage
                  src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                  alt="Modern Architecture"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div ref={statsRef} className="mt-20">
          <div className="bg-white rounded-3xl shadow-xl p-8 md:p-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {aboutData.stats.map((stat, index) => (
                <div
                  key={index}
                  className="stat-card text-center group"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">
                      {index === 0 && '🏗️'}
                      {index === 1 && '⭐'}
                      {index === 2 && '😊'}
                      {index === 3 && '👥'}
                    </span>
                  </div>
                  <div className="stat-value text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
