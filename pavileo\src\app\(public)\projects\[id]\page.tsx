import { <PERSON>ada<PERSON> } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { FaArrowLeft, FaMapMarkerAlt, FaCalendarAlt, FaUser, FaTag } from 'react-icons/fa';

// This would normally be fetched from an API
const projects = [
  {
    id: '1',
    title: 'Modern Villa',
    description: 'Luxury villa with contemporary design and premium finishes.',
    longDescription: 'This modern villa project showcases our commitment to excellence in residential construction. The 5,000 square foot property features 5 bedrooms, 6 bathrooms, a home theater, swimming pool, and landscaped gardens. We used premium materials throughout, including Italian marble, hardwood flooring, and custom cabinetry. The project was completed in 12 months, delivering a stunning contemporary home that perfectly balances luxury, functionality, and comfort.',
    category: 'Residential',
    location: 'Varanasi',
    completionDate: 'January 2023',
    client: 'Private Owner',
    images: [
      'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1600210492493-0946911123ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    ],
    features: [
      '5,000 square feet of living space',
      '5 bedrooms and 6 bathrooms',
      'Home theater and entertainment area',
      'Swimming pool and landscaped gardens',
      'Smart home automation system',
      'Energy-efficient design with solar panels',
      'Custom-designed interiors',
      'Premium materials throughout',
    ],
    challenges: [
      'Complex architectural design requiring precise execution',
      'Integration of smart home technology throughout the property',
      'Custom-designed elements requiring specialized craftsmanship',
      'Strict timeline to meet client\'s move-in date',
    ],
    solutions: [
      'Assembled a specialized team with expertise in luxury home construction',
      'Collaborated with technology specialists for seamless smart home integration',
      'Partnered with skilled artisans for custom elements',
      'Implemented detailed project management to ensure timely completion',
    ],
    featured: true,
  },
  {
    id: '2',
    title: 'Commercial Complex',
    description: 'Multi-story commercial building with modern amenities.',
    longDescription: 'This commercial complex project involved the construction of a 10-story building with retail spaces on the ground floor, office spaces on floors 1-8, and executive suites on the top floor. The 75,000 square foot facility features a contemporary glass facade, energy-efficient systems, and state-of-the-art amenities. The project was completed in 18 months, delivering a modern commercial space that meets the highest standards of functionality, aesthetics, and sustainability.',
    category: 'Commercial',
    location: 'Varanasi',
    completionDate: 'November 2022',
    client: 'ABC Enterprises',
    images: [
      'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1577760258779-e787a1733016?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    ],
    features: [
      '10-story building with 75,000 square feet of space',
      'Retail spaces on ground floor',
      'Office spaces on floors 1-8',
      'Executive suites on top floor',
      'Contemporary glass facade',
      'Energy-efficient HVAC and lighting systems',
      'Underground parking for 200 vehicles',
      'Rooftop garden and meeting area',
    ],
    challenges: [
      'Urban location with limited construction space',
      'Complex foundation requirements due to soil conditions',
      'Integration of multiple building systems',
      'Coordination with numerous tenants for custom build-outs',
    ],
    solutions: [
      'Implemented detailed logistics plan for urban construction',
      'Engaged specialized geotechnical engineers for foundation design',
      'Used Building Information Modeling (BIM) for system integration',
      'Established dedicated tenant coordination team',
    ],
    featured: true,
  },
  {
    id: '3',
    title: 'Apartment Building',
    description: 'Residential apartment complex with 24 luxury units.',
    longDescription: 'This residential apartment complex features 24 luxury units ranging from one to three bedrooms. The project includes premium amenities such as a fitness center, swimming pool, community lounge, and landscaped gardens. We focused on creating spacious, light-filled apartments with high-quality finishes and modern conveniences. The building incorporates energy-efficient systems and sustainable materials throughout. Completed in 15 months, this project delivers comfortable, stylish living spaces in a prime location.',
    category: 'Residential',
    location: 'Varanasi',
    completionDate: 'August 2022',
    client: 'XYZ Developers',
    images: [
      'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1460317442991-0ec209397118?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    ],
    features: [
      '24 luxury apartments (one to three bedrooms)',
      'Fitness center and swimming pool',
      'Community lounge and entertainment area',
      'Landscaped gardens and outdoor spaces',
      'Secure parking garage',
      'Energy-efficient appliances and systems',
      'High-quality finishes throughout',
      'Smart home features in all units',
    ],
    challenges: [
      'Balancing luxury features with budget constraints',
      'Creating unique layouts for different unit types',
      'Maximizing natural light and views for all units',
      'Meeting strict noise reduction requirements',
    ],
    solutions: [
      'Strategic selection of premium materials for high-impact areas',
      'Collaborated with architects to optimize unit layouts',
      'Used 3D modeling to analyze light patterns throughout the year',
      'Implemented advanced acoustic insulation between units',
    ],
    featured: true,
  },
];

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  // In a real app, this would fetch data from an API
  const project = projects.find(p => p.id === params.id);
  
  if (!project) {
    return {
      title: 'Project Not Found',
    };
  }
  
  return {
    title: project.title,
    description: project.description,
  };
}

export default function ProjectDetailPage({ params }: { params: { id: string } }) {
  // In a real app, this would fetch data from an API
  const project = projects.find(p => p.id === params.id);
  
  if (!project) {
    return (
      <main className="pt-20">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">Project Not Found</h1>
            <p className="mb-8">The project you are looking for does not exist.</p>
            <Link 
              href="/projects"
              className="inline-flex items-center text-amber-600 hover:text-amber-700"
            >
              <FaArrowLeft className="mr-2" /> Back to Projects
            </Link>
          </div>
        </div>
      </main>
    );
  }
  
  return (
    <main className="pt-20">
      {/* Hero Section */}
      <section className="relative h-[50vh] md:h-[60vh]">
        <div className="absolute inset-0">
          <Image
            src={project.images[0]}
            alt={project.title}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        
        <div className="relative h-full container mx-auto px-4 md:px-6 flex items-end pb-12">
          <div className="text-white">
            <Link 
              href="/projects"
              className="inline-flex items-center text-amber-400 hover:text-amber-300 mb-4"
            >
              <FaArrowLeft className="mr-2" /> Back to Projects
            </Link>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{project.title}</h1>
            <p className="text-xl text-gray-200 max-w-3xl">{project.description}</p>
          </div>
        </div>
      </section>

      {/* Project Details */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <h2 className="text-3xl font-bold mb-6">Project Overview</h2>
              <p className="text-gray-700 mb-8">{project.longDescription}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div>
                  <h3 className="text-xl font-bold mb-4">Features</h3>
                  <ul className="space-y-2">
                    {project.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-amber-600 mr-3">•</span>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-bold mb-4">Challenges & Solutions</h3>
                  <div className="mb-4">
                    <h4 className="font-bold text-gray-800 mb-2">Challenges:</h4>
                    <ul className="space-y-2">
                      {project.challenges.map((challenge, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-amber-600 mr-3">•</span>
                          <span className="text-gray-700">{challenge}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-800 mb-2">Solutions:</h4>
                    <ul className="space-y-2">
                      {project.solutions.map((solution, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-amber-600 mr-3">•</span>
                          <span className="text-gray-700">{solution}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
              
              <h3 className="text-xl font-bold mb-4">Project Gallery</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {project.images.map((image, index) => (
                  <div key={index} className="relative h-64 rounded-lg overflow-hidden">
                    <Image
                      src={image}
                      alt={`${project.title} - Image ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
            
            {/* Sidebar */}
            <div>
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-xl font-bold mb-4">Project Details</h3>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <FaTag className="text-amber-600 mt-1 mr-3" />
                    <div>
                      <span className="block text-sm text-gray-500">Category</span>
                      <span className="text-gray-800">{project.category}</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <FaMapMarkerAlt className="text-amber-600 mt-1 mr-3" />
                    <div>
                      <span className="block text-sm text-gray-500">Location</span>
                      <span className="text-gray-800">{project.location}</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <FaCalendarAlt className="text-amber-600 mt-1 mr-3" />
                    <div>
                      <span className="block text-sm text-gray-500">Completion Date</span>
                      <span className="text-gray-800">{project.completionDate}</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <FaUser className="text-amber-600 mt-1 mr-3" />
                    <div>
                      <span className="block text-sm text-gray-500">Client</span>
                      <span className="text-gray-800">{project.client}</span>
                    </div>
                  </li>
                </ul>
              </div>
              
              <div className="bg-amber-50 p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4">Interested in a Similar Project?</h3>
                <p className="text-gray-700 mb-6">
                  Contact us today to discuss your project requirements and how we can help bring your vision to life.
                </p>
                <Link 
                  href="/contact"
                  className="block w-full py-2 px-4 bg-amber-600 text-white text-center font-medium rounded-md hover:bg-amber-700 transition-colors"
                >
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Projects */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold mb-8">Related Projects</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {projects
              .filter(p => p.id !== project.id && p.category === project.category)
              .slice(0, 3)
              .map((relatedProject) => (
                <Link
                  key={relatedProject.id}
                  href={`/projects/${relatedProject.id}`}
                  className="block"
                >
                  <div
                    className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                  >
                    <div className="relative h-48">
                      <Image
                        src={relatedProject.images[0]}
                        alt={relatedProject.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-lg font-bold mb-2">{relatedProject.title}</h3>
                      <p className="text-gray-700 mb-4 line-clamp-2">{relatedProject.description}</p>
                      
                      <span className="text-amber-600 font-medium hover:text-amber-700 transition-colors">
                        View Details
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>
    </main>
  );
}
