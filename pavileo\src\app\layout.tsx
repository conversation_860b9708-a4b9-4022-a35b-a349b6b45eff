import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import SessionProvider from "@/components/providers/SessionProvider";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    template: '%s | Pavileo Construction',
    default: 'Pavileo Construction - Building Dreams Into Reality',
  },
  description: "Pavileo is Varanasi's premier construction company, specializing in creating beautiful, functional homes that stand the test of time.",
  keywords: ["construction", "Varanasi", "home building", "residential construction", "commercial construction", "renovation", "interior design", "project management"],
  authors: [{ name: 'Pavileo Construction' }],
  creator: 'Pavileo Construction',
  publisher: 'Pavileo Construction',
  metadataBase: new URL('https://yourdomain.com'),
  openGraph: {
    title: 'Pavileo Construction - Building Dreams Into Reality',
    description: "Pavileo is Varanasi's premier construction company, specializing in creating beautiful, functional homes that stand the test of time.",
    url: 'https://yourdomain.com',
    siteName: 'Pavileo Construction',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Pavileo Construction - Building Dreams Into Reality',
    description: "Pavileo is Varanasi's premier construction company, specializing in creating beautiful, functional homes that stand the test of time.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased`}>
        <SessionProvider>
          <Header />
          {children}
          <Footer />
        </SessionProvider>
      </body>
    </html>
  );
}
