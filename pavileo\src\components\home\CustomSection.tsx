'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import OptimizedImage from '@/components/ui/OptimizedImage';

interface CustomSectionProps {
  data: {
    _id: string;
    title: string;
    subtitle?: string;
    content?: string;
    image?: string;
  };
}

export default function CustomSection({ data }: CustomSectionProps) {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-3xl mx-auto text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {data.title}
          </h2>
          {data.subtitle && (
            <p className="text-lg text-gray-700">
              {data.subtitle}
            </p>
          )}
        </motion.div>

        {data.image && data.content ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative h-[400px] rounded-lg overflow-hidden"
            >
              <OptimizedImage
                src={data.image}
                alt={data.title}
                fill
                className="object-cover"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className="prose prose-lg max-w-none">
                {data.content}
              </div>
            </motion.div>
          </div>
        ) : data.image ? (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative h-[500px] rounded-lg overflow-hidden"
          >
            <Image
              src={data.image}
              alt={data.title}
              fill
              className="object-cover"
            />
          </motion.div>
        ) : data.content ? (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="max-w-3xl mx-auto"
          >
            <div className="prose prose-lg max-w-none">
              {data.content}
            </div>
          </motion.div>
        ) : null}
      </div>
    </section>
  );
}
