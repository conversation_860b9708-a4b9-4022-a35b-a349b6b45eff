'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaArrowLeft, FaEdit } from 'react-icons/fa6';

interface Service {
  id: number;
  title: string;
  description: string;
  icon: string;
  image: string;
  features: string[];
}

export default function ServiceDetail({ params }: { params: { id: string } }) {
  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchService = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        const serviceData = {
          id: parseInt(params.id),
          title: 'Residential Construction',
          description: 'Custom homes built to your specifications with quality materials and expert craftsmanship.',
          icon: 'FaHome',
          image: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
          features: [
            'Custom home design and construction',
            'Luxury villas and bungalows',
            'Apartment buildings and complexes',
            'Affordable housing solutions',
            'Turnkey residential projects'
          ]
        };
        
        setService(serviceData);
      } catch (error) {
        console.error('Error fetching service:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchService();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Service Not Found</h2>
        <p className="text-gray-600 mb-6">The service you are looking for does not exist or has been removed.</p>
        <Link 
          href="/admin/services" 
          className="text-amber-600 hover:text-amber-800 flex items-center justify-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Services
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link 
            href="/admin/services" 
            className="text-amber-600 hover:text-amber-800 mr-4"
          >
            <FaArrowLeft />
          </Link>
          <h1 className="text-2xl font-bold">{service.title}</h1>
        </div>
        <Link 
          href={`/admin/services/${service.id}/edit`} 
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FaEdit className="mr-2" /> Edit Service
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h2 className="text-lg font-semibold mb-2">Service Details</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Icon</p>
                <p className="font-medium">{service.icon}</p>
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">Description</h2>
            <p className="text-gray-700">{service.description}</p>
          </div>
        </div>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Features</h2>
          <ul className="list-disc pl-5 space-y-1">
            {service.features.map((feature, index) => (
              <li key={index} className="text-gray-700">{feature}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h2 className="text-lg font-semibold mb-4">Service Image</h2>
          <div className="relative h-64 rounded-lg overflow-hidden">
            <Image
              src={service.image}
              alt={service.title}
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
}





