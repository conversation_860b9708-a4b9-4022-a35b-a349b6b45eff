'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { getRandomImages } from '@/utils/imageUtils';

interface TeamMember {
  _id: string;
  name: string;
  position: string;
  bio?: string;
  image?: string;
  linkedin?: string;
  email?: string;
}

const defaultTeamMembers: TeamMember[] = [
  {
    _id: '1',
    name: '<PERSON><PERSON>',
    position: 'Founder & CEO',
    bio: 'With over 15 years of experience in construction, <PERSON><PERSON> leads Pavileo with vision and expertise.',
    image: '',
    linkedin: '#',
    email: 'raj<PERSON>@pavileo.com'
  },
  {
    _id: '2',
    name: '<PERSON><PERSON>',
    position: 'Chief Architect',
    bio: '<PERSON><PERSON> brings innovative design solutions and sustainable architecture practices to every project.',
    image: '',
    linkedin: '#',
    email: '<EMAIL>'
  },
  {
    _id: '3',
    name: '<PERSON><PERSON>',
    position: 'Project Manager',
    bio: 'Amit ensures every project is completed on time and within budget with his meticulous planning.',
    image: '',
    linkedin: '#',
    email: '<EMAIL>'
  },
  {
    _id: '4',
    name: '<PERSON><PERSON>',
    position: 'Interior Designer',
    bio: '<PERSON><PERSON> transforms spaces into beautiful, functional environments that exceed client expectations.',
    image: '',
    linkedin: '#',
    email: '<EMAIL>'
  }
];

export default function TeamSection() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call or use default data
    const loadTeamMembers = async () => {
      try {
        // In a real app, this would be an API call
        // const response = await fetch('/api/team');
        // const data = await response.json();
        
        // For now, use default data with dynamic images
        const teamImages = getRandomImages('team', 4);
        const membersWithImages = defaultTeamMembers.map((member, index) => ({
          ...member,
          image: member.image || teamImages[index]
        }));
        
        setTeamMembers(membersWithImages);
      } catch (error) {
        console.error('Error loading team members:', error);
        setTeamMembers(defaultTeamMembers);
      } finally {
        setLoading(false);
      }
    };

    loadTeamMembers();
  }, []);

  if (loading) {
    return (
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-md">
                <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-24 mx-auto mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-32 mx-auto animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Meet Our <span className="text-amber-600">Expert Team</span>
          </motion.h2>
          <motion.p
            className="text-gray-700 text-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Our dedicated professionals bring years of experience and passion to every project.
          </motion.p>
        </div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member._id}
              className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              {/* Profile Image */}
              <div className="relative w-32 h-32 mx-auto mb-4 overflow-hidden rounded-full">
                <OptimizedImage
                  src={member.image}
                  alt={member.name}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                  category="team"
                />
              </div>

              {/* Member Info */}
              <h3 className="text-xl font-semibold mb-2">{member.name}</h3>
              <p className="text-amber-600 font-medium mb-3">{member.position}</p>
              
              {member.bio && (
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">{member.bio}</p>
              )}

              {/* Contact Links */}
              <div className="flex justify-center space-x-3">
                {member.linkedin && (
                  <a
                    href={member.linkedin}
                    className="text-gray-400 hover:text-blue-600 transition-colors"
                    aria-label={`${member.name} LinkedIn`}
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z"/>
                    </svg>
                  </a>
                )}
                {member.email && (
                  <a
                    href={`mailto:${member.email}`}
                    className="text-gray-400 hover:text-amber-600 transition-colors"
                    aria-label={`Email ${member.name}`}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </a>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
