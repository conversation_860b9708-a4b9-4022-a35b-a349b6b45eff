import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Common animation configurations
export const animationConfig = {
  duration: 0.8,
  ease: "power2.out",
  stagger: 0.1,
};

// Fade in from bottom animation
export const fadeInUp = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(element, 
    {
      y: 50,
      opacity: 0,
    },
    {
      y: 0,
      opacity: 1,
      duration: options.duration || animationConfig.duration,
      ease: options.ease || animationConfig.ease,
      delay: options.delay || 0,
      stagger: options.stagger || 0,
      scrollTrigger: options.scrollTrigger ? {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Fade in from left animation
export const fadeInLeft = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(element,
    {
      x: -50,
      opacity: 0,
    },
    {
      x: 0,
      opacity: 1,
      duration: options.duration || animationConfig.duration,
      ease: options.ease || animationConfig.ease,
      delay: options.delay || 0,
      scrollTrigger: options.scrollTrigger ? {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Fade in from right animation
export const fadeInRight = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(element,
    {
      x: 50,
      opacity: 0,
    },
    {
      x: 0,
      opacity: 1,
      duration: options.duration || animationConfig.duration,
      ease: options.ease || animationConfig.ease,
      delay: options.delay || 0,
      scrollTrigger: options.scrollTrigger ? {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Scale in animation
export const scaleIn = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(element,
    {
      scale: 0,
      opacity: 0,
    },
    {
      scale: 1,
      opacity: 1,
      duration: options.duration || animationConfig.duration,
      ease: options.ease || "back.out(1.7)",
      delay: options.delay || 0,
      stagger: options.stagger || 0,
      scrollTrigger: options.scrollTrigger ? {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Rotate in animation
export const rotateIn = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(element,
    {
      rotation: -180,
      scale: 0,
      opacity: 0,
    },
    {
      rotation: 0,
      scale: 1,
      opacity: 1,
      duration: options.duration || animationConfig.duration,
      ease: options.ease || "back.out(1.7)",
      delay: options.delay || 0,
      scrollTrigger: options.scrollTrigger ? {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Hover animations
export const hoverScale = (element: string | Element, scale: number = 1.05) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element;
  if (!el) return;

  el.addEventListener('mouseenter', () => {
    gsap.to(el, { scale, duration: 0.3, ease: "power2.out" });
  });

  el.addEventListener('mouseleave', () => {
    gsap.to(el, { scale: 1, duration: 0.3, ease: "power2.out" });
  });
};

// Hover lift animation
export const hoverLift = (element: string | Element, y: number = -10) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element;
  if (!el) return;

  el.addEventListener('mouseenter', () => {
    gsap.to(el, { 
      y, 
      boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
      duration: 0.3, 
      ease: "power2.out" 
    });
  });

  el.addEventListener('mouseleave', () => {
    gsap.to(el, { 
      y: 0, 
      boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
      duration: 0.3, 
      ease: "power2.out" 
    });
  });
};

// Stagger animation for multiple elements
export const staggerAnimation = (elements: string, options: any = {}) => {
  return gsap.fromTo(elements,
    {
      y: 50,
      opacity: 0,
      scale: 0.8,
    },
    {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: options.duration || 0.6,
      ease: options.ease || "power2.out",
      stagger: options.stagger || 0.1,
      scrollTrigger: options.scrollTrigger ? {
        trigger: elements,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Text reveal animation
export const textReveal = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(element,
    {
      y: 100,
      opacity: 0,
    },
    {
      y: 0,
      opacity: 1,
      duration: options.duration || 1,
      ease: options.ease || "power3.out",
      delay: options.delay || 0,
      scrollTrigger: options.scrollTrigger ? {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
        ...options.scrollTrigger
      } : undefined
    }
  );
};

// Counter animation
export const animateCounter = (element: string | Element, endValue: number, options: any = {}) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element;
  if (!el) return;

  const obj = { value: 0 };
  return gsap.to(obj, {
    value: endValue,
    duration: options.duration || 2,
    ease: options.ease || "power2.out",
    onUpdate: () => {
      el.textContent = Math.round(obj.value).toString() + (options.suffix || '');
    },
    scrollTrigger: options.scrollTrigger ? {
      trigger: element,
      start: "top 80%",
      toggleActions: "play none none reverse",
      ...options.scrollTrigger
    } : undefined
  });
};

// Cleanup function
export const cleanupAnimations = () => {
  ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  gsap.killTweensOf("*");
};
