'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import OptimizedImage from './OptimizedImage';
import DetailModal from './DetailModal';
import { FaTimes, FaMapMarkerAlt, FaCalendarAlt, FaUser, FaBuilding } from 'react-icons/fa';
import { getImageWithFallback, generateImageGallery } from '@/utils/imageUtils';

interface Project {
  _id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  images: string[];
  completionDate?: string;
  client?: string;
  detailedDescription?: string;
  features?: string[];
}

interface ProjectModalProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
}

const ProjectModal: React.FC<ProjectModalProps> = ({ project, isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [detailModalType, setDetailModalType] = useState<'learn-more' | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  useEffect(() => {
    if (isOpen && modalRef.current && overlayRef.current && contentRef.current) {
      // Animate modal opening
      gsap.set(modalRef.current, { display: 'flex' });
      gsap.fromTo(overlayRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.3, ease: "power2.out" }
      );
      gsap.fromTo(contentRef.current,
        { opacity: 0, scale: 0.8, y: 50 },
        { opacity: 1, scale: 1, y: 0, duration: 0.4, ease: "back.out(1.7)", delay: 0.1 }
      );

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else if (!isOpen && modalRef.current && overlayRef.current && contentRef.current) {
      // Animate modal closing
      gsap.to(contentRef.current, {
        opacity: 0,
        scale: 0.8,
        y: 50,
        duration: 0.3,
        ease: "power2.in"
      });
      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          if (modalRef.current) {
            gsap.set(modalRef.current, { display: 'none' });
          }
        }
      });

      // Restore body scroll
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!project) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleViewGallery = () => {
    // Convert project to service-like format for DetailModal
    const projectAsService = {
      _id: project._id,
      title: project.title,
      description: project.description,
      detailedDescription: project.detailedDescription || `Explore the details of ${project.title}, a ${project.category.toLowerCase()} project completed in ${project.location}. See our construction quality and attention to detail.`,
      features: project.features || [
        `${project.category} construction expertise`,
        'High-quality materials used',
        'Professional finishing work',
        'Attention to detail',
        'Client satisfaction guaranteed'
      ],
      image: project.images[0],
      icon: 'building'
    };

    setDetailModalType('learn-more');
    setIsDetailModalOpen(true);
  };

  const handleDetailModalClose = () => {
    setIsDetailModalOpen(false);
    setDetailModalType(null);
  };

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{ display: 'none' }}
    >
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={handleOverlayClick}
      />

      {/* Modal Content */}
      <div
        ref={contentRef}
        className="relative bg-white rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors duration-200"
        >
          <FaTimes className="text-gray-600" />
        </button>

        {/* Modal Body */}
        <div className="grid grid-cols-1 lg:grid-cols-3 h-full">
          {/* Image Section */}
          <div className="lg:col-span-2 relative h-64 lg:h-full">
            <OptimizedImage
              src={getImageWithFallback(project.images[0], project.category.toLowerCase() as any)}
              alt={project.title}
              fill
              className="object-cover"
              category={project.category.toLowerCase() as any}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

            {/* Category Badge */}
            <div className="absolute top-6 left-6">
              <span className="bg-amber-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                {project.category}
              </span>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8 overflow-y-auto">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {project.title}
                </h2>
                <p className="text-gray-700 leading-relaxed">
                  {project.detailedDescription || project.description}
                </p>
              </div>

              {/* Project Details */}
              <div className="space-y-4">
                <div className="flex items-center text-gray-600">
                  <FaMapMarkerAlt className="w-4 h-4 mr-3 text-amber-600" />
                  <span>{project.location}</span>
                </div>

                {project.completionDate && (
                  <div className="flex items-center text-gray-600">
                    <FaCalendarAlt className="w-4 h-4 mr-3 text-amber-600" />
                    <span>Completed: {project.completionDate}</span>
                  </div>
                )}

                {project.client && (
                  <div className="flex items-center text-gray-600">
                    <FaUser className="w-4 h-4 mr-3 text-amber-600" />
                    <span>Client: {project.client}</span>
                  </div>
                )}
              </div>

              {project.features && project.features.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    Project Features:
                  </h3>
                  <ul className="space-y-2">
                    {project.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-amber-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="pt-6 border-t border-gray-200">
                <div className="flex justify-center">
                  <button
                    onClick={handleViewGallery}
                    className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
                  >
                    View Gallery
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detail Modal */}
      {detailModalType && (
        <DetailModal
          isOpen={isDetailModalOpen}
          onClose={handleDetailModalClose}
          type="learn-more"
          service={{
            _id: project._id,
            title: project.title,
            description: project.description,
            detailedDescription: project.detailedDescription || `Explore the details of ${project.title}, a ${project.category.toLowerCase()} project completed in ${project.location}. See our construction quality and attention to detail.`,
            features: project.features || [
              `${project.category} construction expertise`,
              'High-quality materials used',
              'Professional finishing work',
              'Attention to detail',
              'Client satisfaction guaranteed'
            ],
            image: project.images[0],
            icon: 'building'
          }}
        />
      )}
    </div>
  );
};

export default ProjectModal;
