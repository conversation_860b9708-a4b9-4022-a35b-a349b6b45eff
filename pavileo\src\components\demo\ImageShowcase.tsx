'use client';

import { useState } from 'react';
import OptimizedImage from '@/components/ui/OptimizedImage';
import Logo from '@/components/ui/Logo';
import { 
  getRandomImage, 
  getRandomImages, 
  getImageWithFallback,
  getLocalPlaceholder,
  brandingImages,
  imageCategories 
} from '@/utils/imageUtils';

export default function ImageShowcase() {
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof imageCategories>('construction');
  
  const categories = Object.keys(imageCategories) as (keyof typeof imageCategories)[];
  const randomImages = getRandomImages(selectedCategory, 6);

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">
          🎨 Dynamic Images System <span className="text-amber-600">Showcase</span>
        </h1>
        
        {/* Logo Variants */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Logo Variants</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <h3 className="text-lg font-medium mb-4">Default Logo</h3>
              <Logo variant="default" size="lg" />
            </div>
            <div className="bg-gray-800 p-6 rounded-lg shadow-md text-center">
              <h3 className="text-lg font-medium mb-4 text-white">White Logo</h3>
              <Logo variant="white" size="lg" />
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <h3 className="text-lg font-medium mb-4">Logo Only</h3>
              <Logo variant="default" size="lg" showText={false} />
            </div>
          </div>
        </section>

        {/* Category Selection */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Image Categories</h2>
          <div className="flex flex-wrap gap-3 mb-6">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full font-medium transition-all ${
                  selectedCategory === category
                    ? 'bg-amber-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
          
          {/* Category Images Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {randomImages.map((image, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="relative h-48">
                  <OptimizedImage
                    src={image}
                    alt={`${selectedCategory} image ${index + 1}`}
                    fill
                    className="object-cover"
                    category={selectedCategory}
                  />
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-gray-800">
                    {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Image {index + 1}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Dynamic image with fallback support
                  </p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Local Placeholders */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Local Placeholder Images</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {categories.map((category) => (
              <div key={category} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="relative h-32">
                  <OptimizedImage
                    src={getLocalPlaceholder(category)}
                    alt={`${category} placeholder`}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-3">
                  <h4 className="text-sm font-medium text-gray-800 capitalize">
                    {category}
                  </h4>
                  <p className="text-xs text-gray-600">Local SVG</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Fallback Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Fallback System Demo</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <OptimizedImage
                  src="https://invalid-url-that-will-fail.com/image.jpg"
                  alt="This will show fallback"
                  fill
                  className="object-cover"
                  category="residential"
                />
              </div>
              <div className="p-4">
                <h3 className="font-medium text-gray-800">Broken URL Test</h3>
                <p className="text-sm text-gray-600">
                  This image URL is intentionally broken to show fallback behavior
                </p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="relative h-48">
                <OptimizedImage
                  src=""
                  alt="Empty src test"
                  fill
                  className="object-cover"
                  category="commercial"
                />
              </div>
              <div className="p-4">
                <h3 className="font-medium text-gray-800">Empty Source Test</h3>
                <p className="text-sm text-gray-600">
                  No source provided - shows category-appropriate fallback
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Examples */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Usage Examples</h2>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-800 mb-2">Basic Usage:</h3>
                <code className="bg-gray-100 p-2 rounded text-sm block">
                  {`<OptimizedImage src={imageUrl} alt="Description" fill category="residential" />`}
                </code>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-800 mb-2">With Fallback:</h3>
                <code className="bg-gray-100 p-2 rounded text-sm block">
                  {`const imageUrl = getImageWithFallback(project.image, 'commercial');`}
                </code>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-800 mb-2">Generate Gallery:</h3>
                <code className="bg-gray-100 p-2 rounded text-sm block">
                  {`const gallery = generateImageGallery('service-1', 'interior', 4);`}
                </code>
              </div>
            </div>
          </div>
        </section>

        {/* Stats */}
        <section className="text-center">
          <div className="bg-amber-50 rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">System Stats</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-3xl font-bold text-amber-600">
                  {Object.keys(imageCategories).length}
                </div>
                <div className="text-sm text-gray-600">Categories</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-amber-600">
                  {Object.values(imageCategories).flat().length}
                </div>
                <div className="text-sm text-gray-600">External Images</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-amber-600">
                  {Object.keys(brandingImages).length}
                </div>
                <div className="text-sm text-gray-600">Local Assets</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-amber-600">∞</div>
                <div className="text-sm text-gray-600">Combinations</div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
