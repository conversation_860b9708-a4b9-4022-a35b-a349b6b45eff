'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaEdit, FaTrash, FaPlus, FaEye, FaEyeSlash, FaArrowUp, FaArrowDown } from 'react-icons/fa';

interface Section {
  _id: string;
  title: string;
  subtitle?: string;
  content?: string;
  image?: string;
  type: 'hero' | 'about' | 'services' | 'projects' | 'testimonials' | 'contact' | 'custom';
  position: number;
  isActive: boolean;
  createdAt: string;
}

export default function AdminSections() {
  const [sections, setSections] = useState<Section[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSections = async () => {
      try {
        const response = await fetch('/api/sections');
        if (!response.ok) {
          throw new Error('Failed to fetch sections');
        }
        const data = await response.json();
        setSections(data);
      } catch (error) {
        console.error('Error fetching sections:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSections();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this section?')) {
      return;
    }

    try {
      const response = await fetch(`/api/sections/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete section');
      }

      setSections(sections.filter(section => section._id !== id));
    } catch (error) {
      console.error('Error deleting section:', error);
      alert('Failed to delete section');
    }
  };

  const toggleActive = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/sections/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !currentStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update section');
      }

      setSections(sections.map(section => 
        section._id === id ? { ...section, isActive: !currentStatus } : section
      ));
    } catch (error) {
      console.error('Error updating section:', error);
      alert('Failed to update section');
    }
  };

  const moveSection = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = sections.findIndex(section => section._id === id);
    if (
      (direction === 'up' && currentIndex === 0) || 
      (direction === 'down' && currentIndex === sections.length - 1)
    ) {
      return;
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const targetSection = sections[newIndex];

    try {
      // Update the current section's position
      await fetch(`/api/sections/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ position: targetSection.position }),
      });

      // Update the target section's position
      await fetch(`/api/sections/${targetSection._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ position: sections[currentIndex].position }),
      });

      // Update the local state
      const newSections = [...sections];
      [newSections[currentIndex], newSections[newIndex]] = [newSections[newIndex], newSections[currentIndex]];
      setSections(newSections);
    } catch (error) {
      console.error('Error moving section:', error);
      alert('Failed to move section');
    }
  };

  const getSectionTypeLabel = (type: string) => {
    switch (type) {
      case 'hero':
        return 'Hero Banner';
      case 'about':
        return 'About Us';
      case 'services':
        return 'Services';
      case 'projects':
        return 'Projects';
      case 'testimonials':
        return 'Testimonials';
      case 'contact':
        return 'Contact';
      case 'custom':
        return 'Custom Section';
      default:
        return type;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Sections</h1>
        <Link 
          href="/admin/sections/new" 
          className="bg-amber-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FaPlus className="mr-2" /> Add Section
        </Link>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {sections.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6 text-center text-gray-500">
              No sections found. Click "Add Section" to create your first section.
            </div>
          ) : (
            sections.map((section, index) => (
              <div 
                key={section._id} 
                className={`bg-white rounded-lg shadow-md overflow-hidden ${!section.isActive ? 'opacity-60' : ''}`}
              >
                <div className="flex flex-col md:flex-row">
                  {section.image && (
                    <div className="md:w-1/4 relative h-48 md:h-auto">
                      <Image
                        src={section.image}
                        alt={section.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  
                  <div className={`p-6 ${section.image ? 'md:w-3/4' : 'w-full'}`}>
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h2 className="text-xl font-bold">{section.title}</h2>
                        {section.subtitle && (
                          <p className="text-gray-600 mt-1">{section.subtitle}</p>
                        )}
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        section.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {section.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    
                    <div className="flex items-center text-sm text-gray-500 mb-4">
                      <span className="mr-4">Type: {getSectionTypeLabel(section.type)}</span>
                      <span>Position: {section.position}</span>
                    </div>
                    
                    {section.content && (
                      <div className="mb-4">
                        <p className="text-gray-700 line-clamp-2">{section.content}</p>
                      </div>
                    )}
                    
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => moveSection(section._id, 'up')}
                        disabled={index === 0}
                        className={`p-2 rounded-full ${
                          index === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'
                        }`}
                        title="Move Up"
                      >
                        <FaArrowUp />
                      </button>
                      <button
                        onClick={() => moveSection(section._id, 'down')}
                        disabled={index === sections.length - 1}
                        className={`p-2 rounded-full ${
                          index === sections.length - 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'
                        }`}
                        title="Move Down"
                      >
                        <FaArrowDown />
                      </button>
                      <button
                        onClick={() => toggleActive(section._id, section.isActive)}
                        className="p-2 rounded-full text-amber-600 hover:bg-amber-50"
                        title={section.isActive ? 'Deactivate' : 'Activate'}
                      >
                        {section.isActive ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      <Link 
                        href={`/admin/sections/${section._id}/edit`}
                        className="p-2 rounded-full text-blue-600 hover:bg-blue-50"
                        title="Edit"
                      >
                        <FaEdit />
                      </Link>
                      <button 
                        onClick={() => handleDelete(section._id)}
                        className="p-2 rounded-full text-red-600 hover:bg-red-50"
                        title="Delete"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
}
