'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaArrowLeft, FaEdit } from 'react-icons/fa';

interface Testimonial {
  _id: string;
  name: string;
  position: string;
  content: string;
  rating: number;
  image: string;
}

export default function TestimonialDetail({ params }: { params: { id: string } }) {
  const [testimonial, setTestimonial] = useState<Testimonial | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonial = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        const testimonialData = {
          _id: params.id,
          name: '<PERSON><PERSON>',
          position: 'Homeowner',
          content: 'Pavileo Construction built our dream home with exceptional quality and attention to detail. The team was professional, responsive, and delivered on time. We are extremely satisfied with the results and would highly recommend their services to anyone looking to build a new home or renovate an existing property.',
          rating: 5,
          image: 'https://images.unsplash.com/photo-1618151313441-bc79b11e5090?q=80&w=2787&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        };
        
        setTestimonial(testimonialData);
      } catch (error) {
        console.error('Error fetching testimonial:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonial();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (!testimonial) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Testimonial Not Found</h2>
        <p className="text-gray-600 mb-6">The testimonial you are looking for does not exist or has been removed.</p>
        <Link 
          href="/admin/testimonials" 
          className="text-amber-600 hover:text-amber-800 flex items-center justify-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Testimonials
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link 
            href="/admin/testimonials" 
            className="text-amber-600 hover:text-amber-800 mr-4"
          >
            <FaArrowLeft />
          </Link>
          <h1 className="text-2xl font-bold">Testimonial Details</h1>
        </div>
        <Link 
          href={`/admin/testimonials/${testimonial._id}/edit`} 
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FaEdit className="mr-2" /> Edit Testimonial
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-6">
          <div className="relative w-24 h-24 rounded-full overflow-hidden mr-6">
            <Image
              src={testimonial.image}
              alt={testimonial.name}
              fill
              className="object-cover"
            />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">{testimonial.name}</h2>
            <p className="text-gray-600">{testimonial.position}</p>
            <div className="flex mt-2">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-5 h-5 ${
                    i < testimonial.rating ? 'text-amber-500' : 'text-gray-300'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              ))}
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-2">Testimonial Content</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-gray-700">{testimonial.content}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
