'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaEdit, FaTrash, Fa<PERSON>ye, FaPlus } from 'react-icons/fa';

interface Testimonial {
  _id: string;
  name: string;
  position: string;
  content: string;
  rating: number;
  image: string;
}

export default function AdminTestimonials() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        setTestimonials([
          {
            _id: '1',
            name: '<PERSON><PERSON>',
            position: 'Homeowner',
            content: 'Pavileo Construction built our dream home with exceptional quality and attention to detail. The team was professional, responsive, and delivered on time.',
            rating: 5,
            image: 'https://images.unsplash.com/photo-1618151313441-bc79b11e5090?q=80&w=2787&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          },
          {
            _id: '2',
            name: 'Priya Patel',
            position: 'Business Owner',
            content: 'We hired Pavileo for our office renovation and they delivered exceptional results. Professional team, on-time completion, and excellent craftsmanship.',
            rating: 5,
            image: 'https://images.unsplash.com/photo-1592124549776-a7f0cc973b24?q=80&w=2787&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          },
          {
            _id: '3',
            name: 'Amit Verma',
            position: 'Property Developer',
            content: 'Working with Pavileo on our apartment complex was a great experience. Their expertise and project management skills are outstanding.',
            rating: 4,
            image: 'https://images.unsplash.com/photo-1578357078586-491adf1aa5ba?q=80&w=2864&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          },
        ]);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  const handleDelete = (id: string) => {
    // In a real application, you would call your API to delete the testimonial
    setTestimonials(testimonials.filter(testimonial => testimonial._id !== id));
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Testimonials</h1>
        <Link 
          href="/admin/testimonials/new" 
          className="bg-amber-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FaPlus className="mr-2" /> Add Testimonial
        </Link>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div key={testimonial._id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden mr-4">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900">{testimonial.name}</h3>
                    <p className="text-sm text-gray-600">{testimonial.position}</p>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex mb-2">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={`w-5 h-5 ${
                          i < testimonial.rating ? 'text-amber-500' : 'text-gray-300'
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    ))}
                  </div>
                  <p className="text-gray-700 line-clamp-3">{testimonial.content}</p>
                </div>
                
                <div className="flex justify-end space-x-2">
                  <Link 
                    href={`/admin/testimonials/${testimonial._id}`}
                    className="text-amber-600 hover:text-amber-900"
                  >
                    <FaEye />
                  </Link>
                  <Link 
                    href={`/admin/testimonials/${testimonial._id}/edit`}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <FaEdit />
                  </Link>
                  <button 
                    onClick={() => handleDelete(testimonial._id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
