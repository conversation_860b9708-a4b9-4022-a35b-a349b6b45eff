/**
 * This script checks for common errors in the codebase.
 * Run it with: node src/scripts/check-errors.js
 */

const fs = require('fs');
const path = require('path');

// Define paths to check
const pathsToCheck = [
  'src/app/api/auth/[...nextauth]/route.ts',
  'src/app/api/projects/route.ts',
  'src/app/api/projects/[id]/route.ts',
  'src/app/api/services/route.ts',
  'src/app/api/testimonials/route.ts',
  'src/app/api/contact/route.ts',
  'src/app/admin/layout.tsx',
  'next.config.ts',
];

// Define patterns to check for
const patterns = [
  {
    name: 'authOptions export',
    regex: /export\s+const\s+authOptions/,
    files: ['src/app/api/auth/[...nextauth]/route.ts'],
    required: true,
  },
  {
    name: 'authOptions import',
    regex: /import\s+{\s*authOptions\s*}\s+from\s+['"]@\/app\/api\/auth\/\[\.\.\.\s*nextauth\]\/route['"]/,
    files: [
      'src/app/api/projects/route.ts',
      'src/app/api/projects/[id]/route.ts',
      'src/app/api/services/route.ts',
      'src/app/api/testimonials/route.ts',
      'src/app/api/contact/route.ts',
      'src/app/admin/layout.tsx',
    ],
    required: true,
  },
  {
    name: 'Image remotePatterns',
    regex: /remotePatterns\s*:\s*\[\s*{\s*protocol\s*:\s*['"]https['"]/,
    files: ['next.config.ts'],
    required: true,
  },
];

// Check files
let hasErrors = false;

for (const pattern of patterns) {
  for (const file of pattern.files) {
    const filePath = path.join(process.cwd(), file);
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const matches = content.match(pattern.regex);
      
      if (pattern.required && !matches) {
        console.error(`❌ Error: ${pattern.name} not found in ${file}`);
        hasErrors = true;
      } else if (!pattern.required && matches) {
        console.error(`❌ Error: ${pattern.name} found in ${file} but should not be present`);
        hasErrors = true;
      } else {
        console.log(`✅ ${pattern.name} check passed for ${file}`);
      }
    } catch (error) {
      console.error(`❌ Error reading file ${file}: ${error.message}`);
      hasErrors = true;
    }
  }
}

if (hasErrors) {
  console.error('\n❌ Errors found. Please fix them before continuing.');
  process.exit(1);
} else {
  console.log('\n✅ All checks passed!');
}
