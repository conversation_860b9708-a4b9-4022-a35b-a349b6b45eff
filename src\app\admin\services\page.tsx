'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaEdit, FaTrash, FaEye, FaPlus, FaHome, FaBuilding, FaPaintRoller, FaRulerCombined, FaClipboardCheck } from 'react-icons/fa';

interface Service {
  id: number;
  title: string;
  description: string;
  icon: string;
}

export default function AdminServices() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Map icon names to actual icon components
  const iconMap: Record<string, React.ReactNode> = {
    FaHome: <FaHome className="text-amber-600" />,
    FaBuilding: <FaBuilding className="text-amber-600" />,
    FaPaintRoller: <FaPaintRoller className="text-amber-600" />,
    FaRulerCombined: <FaRulerCombined className="text-amber-600" />,
    FaClipboardCheck: <FaClipboardCheck className="text-amber-600" />,
  };

  useEffect(() => {
    const fetchServices = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        setServices([
          {
            id: 1,
            title: 'Residential Construction',
            description: 'Custom homes built to your specifications with quality materials and expert craftsmanship.',
            icon: 'FaHome',
          },
          {
            id: 2,
            title: 'Commercial Construction',
            description: 'Functional and attractive commercial spaces designed to meet your business needs.',
            icon: 'FaBuilding',
          },
          {
            id: 3,
            title: 'Renovation & Remodeling',
            description: 'Transform your existing space with our comprehensive renovation services.',
            icon: 'FaPaintRoller',
          },
          {
            id: 4,
            title: 'Interior Design',
            description: 'Professional interior design services to create beautiful and functional spaces.',
            icon: 'FaRulerCombined',
          },
          {
            id: 5,
            title: 'Project Management',
            description: 'End-to-end project management ensuring timely completion within budget.',
            icon: 'FaClipboardCheck',
          },
        ]);
      } catch (error) {
        console.error('Error fetching services:', error);
        setError('Failed to load services. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Error</h2>
        <p className="text-gray-600 mb-6">{error}</p>
      </div>
    );
  }

  const handleDelete = (id: number) => {
    // In a real application, you would call your API to delete the service
    setServices(services.filter((service: Service) => service.id !== id));
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Services</h1>
        <Link
          href="/admin/services/new"
          className="bg-amber-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FaPlus className="mr-2" /> Add Service
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Icon
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {services.map((service: Service) => (
              <tr key={service.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{service.title}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-500 line-clamp-2">{service.description}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {iconMap[service.icon] || service.icon}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <Link
                      href={`/admin/services/${service.id}`}
                      className="text-amber-600 hover:text-amber-900"
                    >
                      <FaEye />
                    </Link>
                    <Link
                      href={`/admin/services/${service.id}/edit`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <FaEdit />
                    </Link>
                    <button
                      onClick={() => handleDelete(service.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}


