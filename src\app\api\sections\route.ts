import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/dbConnect';
import Section from '@/models/Section';

// GET /api/sections - Get all sections
export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');
    const isActive = searchParams.get('isActive');
    
    const query: any = {};
    
    if (type) {
      query.type = type;
    }
    
    if (isActive === 'true') {
      query.isActive = true;
    } else if (isActive === 'false') {
      query.isActive = false;
    }
    
    const sections = await Section.find(query).sort({ position: 1 });
    
    return NextResponse.json(sections);
  } catch (error) {
    console.error('Error fetching sections:', error);
    return NextResponse.json({ error: 'Failed to fetch sections' }, { status: 500 });
  }
}

// POST /api/sections - Create a new section (admin only)
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    await dbConnect();
    
    const data = await req.json();
    
    // Get the highest position value to place the new section at the end
    const highestPositionSection = await Section.findOne({}).sort({ position: -1 });
    const highestPosition = highestPositionSection ? highestPositionSection.position : 0;
    
    data.position = data.position ?? highestPosition + 1;
    
    const section = await Section.create(data);
    
    return NextResponse.json(section, { status: 201 });
  } catch (error: any) {
    console.error('Error creating section:', error);
    return NextResponse.json({ error: error.message || 'Failed to create section' }, { status: 500 });
  }
}
