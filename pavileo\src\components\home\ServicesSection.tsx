'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { FaHome, FaBuilding, FaPaintRoller, FaRulerCombined, FaClipboardCheck } from 'react-icons/fa';
import Button from '@/components/ui/Button';
import OptimizedImage from '@/components/ui/OptimizedImage';
import ServiceModal from '@/components/ui/ServiceModal';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { getServiceImage, getImageWithFallback } from '@/utils/imageUtils';

interface Service {
  _id: string;
  title: string;
  description: string;
  icon: string;
  image?: string;
  features?: string[];
  detailedDescription?: string;
}

const defaultServices = [
  {
    _id: '1',
    title: 'Residential Construction',
    description: 'Custom homes built to your specifications with quality materials and expert craftsmanship.',
    icon: 'home',
    image: 'https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Custom home design and construction',
      'Luxury villas and bungalows',
      'Apartment buildings and complexes',
      'Affordable housing solutions',
      'Turnkey residential projects'
    ],
    detailedDescription: 'We specialize in creating dream homes that reflect your unique style and needs. From luxury villas to affordable housing, our residential construction services cover every aspect of home building with attention to detail and quality craftsmanship.'
  },
  {
    _id: '2',
    title: 'Commercial Construction',
    description: 'Functional and attractive commercial spaces designed to meet your business needs.',
    icon: 'building',
    image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Office buildings and corporate headquarters',
      'Retail spaces and shopping centers',
      'Hotels and hospitality projects',
      'Educational institutions',
      'Healthcare facilities'
    ],
    detailedDescription: 'Our commercial construction expertise spans across various sectors, delivering functional and aesthetically pleasing business environments that enhance productivity and customer experience.'
  },
  {
    _id: '3',
    title: 'Renovation & Remodeling',
    description: 'Transform your existing space with our comprehensive renovation services.',
    icon: 'paintRoller',
    image: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Complete home renovations',
      'Kitchen and bathroom remodeling',
      'Commercial space renovations',
      'Historic building restoration',
      'Structural repairs and upgrades'
    ],
    detailedDescription: 'Breathe new life into your existing spaces with our renovation and remodeling services. We transform outdated areas into modern, functional spaces while preserving the character and value of your property.'
  },
  {
    _id: '4',
    title: 'Interior Design',
    description: 'Professional interior design services to create beautiful and functional spaces.',
    icon: 'rulerCombined',
    image: 'https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Residential interior design',
      'Commercial interior design',
      'Space planning and optimization',
      'Custom furniture design',
      'Material and finish selection'
    ],
    detailedDescription: 'Our interior design team creates stunning, functional spaces that reflect your personality and lifestyle. From concept to completion, we handle every detail to ensure your space is both beautiful and practical.'
  },
  {
    _id: '5',
    title: 'Project Management',
    description: 'End-to-end project management ensuring timely completion within budget.',
    icon: 'clipboardCheck',
    image: 'https://images.unsplash.com/photo-1507537297725-24a1c029d3ca?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Comprehensive project planning',
      'Budget development and management',
      'Schedule development and tracking',
      'Quality control and assurance',
      'Risk management and mitigation'
    ],
    detailedDescription: 'Our experienced project managers ensure your construction project runs smoothly from start to finish. We coordinate all aspects of the build, keeping you informed and ensuring quality standards are met.'
  },
];

const iconMap: Record<string, React.ReactNode> = {
  home: <FaHome size={40} className="text-amber-600" />,
  building: <FaBuilding size={40} className="text-amber-600" />,
  paintRoller: <FaPaintRoller size={40} className="text-amber-600" />,
  rulerCombined: <FaRulerCombined size={40} className="text-amber-600" />,
  clipboardCheck: <FaClipboardCheck size={40} className="text-amber-600" />,
};

const ServicesSection = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Refs for GSAP animations
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await fetch('/api/services');
        if (response.ok) {
          const data = await response.json();
          setServices(data);
        } else {
          // If API fails, use default services
          setServices(defaultServices);
        }
      } catch (error) {
        console.error('Error fetching services:', error);
        setServices(defaultServices);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // GSAP animations
  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }

    // Animate section elements
    if (titleRef.current && subtitleRef.current) {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });

      tl.fromTo(titleRef.current,
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }
      )
      .fromTo(subtitleRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
        "-=0.4"
      );
    }
  }, []);

  // Animate service cards
  useEffect(() => {
    if (cardsRef.current && !loading) {
      const cards = cardsRef.current.querySelectorAll('.service-card');

      gsap.fromTo(cards,
        { y: 80, opacity: 0, scale: 0.8, rotation: -5 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotation: 0,
          duration: 0.8,
          stagger: 0.15,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: cardsRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Add advanced hover effects
      cards.forEach((card, index) => {
        const cardElement = card as HTMLElement;
        const icon = cardElement.querySelector('.service-icon');
        const button = cardElement.querySelector('.service-button');
        const image = cardElement.querySelector('img');

        cardElement.addEventListener('mouseenter', () => {
          gsap.to(cardElement, {
            y: -15,
            scale: 1.02,
            boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
            duration: 0.4,
            ease: "power2.out"
          });

          if (icon) {
            gsap.to(icon, {
              rotation: 360,
              scale: 1.1,
              duration: 0.6,
              ease: "back.out(1.7)"
            });
          }

          if (button) {
            gsap.to(button, {
              scale: 1.05,
              backgroundColor: "#d97706",
              color: "#ffffff",
              duration: 0.3,
              ease: "power2.out"
            });
          }

          if (image) {
            gsap.to(image, {
              scale: 1.1,
              duration: 0.4,
              ease: "power2.out"
            });
          }
        });

        cardElement.addEventListener('mouseleave', () => {
          gsap.to(cardElement, {
            y: 0,
            scale: 1,
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
            duration: 0.4,
            ease: "power2.out"
          });

          if (icon) {
            gsap.to(icon, {
              rotation: 0,
              scale: 1,
              duration: 0.4,
              ease: "power2.out"
            });
          }

          if (button) {
            gsap.to(button, {
              scale: 1,
              backgroundColor: "transparent",
              color: "#d97706",
              duration: 0.3,
              ease: "power2.out"
            });
          }

          if (image) {
            gsap.to(image, {
              scale: 1,
              duration: 0.4,
              ease: "power2.out"
            });
          }
        });
      });
    }
  }, [services, loading]);

  // Use default services if API call is still loading
  const displayServices = loading ? defaultServices : services.length > 0 ? services : defaultServices;

  // Modal handlers
  const handleServiceClick = (service: Service) => {
    setSelectedService(service);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedService(null);
  };

  return (
    <section ref={sectionRef} className="py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2
            ref={titleRef}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            Our <span className="text-amber-600">Services</span>
          </h2>

          <p
            ref={subtitleRef}
            className="text-gray-700"
          >
            We offer a comprehensive range of construction services to meet all your building needs.
          </p>
        </div>

        <div ref={cardsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayServices.map((service, index) => (
            <div
              key={service._id}
              className="service-card bg-white rounded-lg shadow-md transition-all duration-300 group relative overflow-hidden cursor-pointer"
              onClick={() => handleServiceClick(service)}
            >
                {/* Service Image */}
                <div className="relative h-48 overflow-hidden">
                  <OptimizedImage
                    src={getImageWithFallback(service.image, 'construction')}
                    alt={service.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    category="construction"
                  />
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

                  {/* Icon overlay */}
                  <div className="absolute top-4 right-4 service-icon">
                    <div className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg">
                      {iconMap[service.icon] || <FaHome size={24} className="text-amber-600" />}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 group-hover:text-amber-700 transition-colors duration-300">
                    {service.title}
                  </h3>

                  <p className="text-gray-700 mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  <div className="flex justify-center">
                    <span className="service-button inline-flex items-center justify-center px-6 py-3 border-2 border-amber-600 text-amber-600 rounded-full font-medium transition-all duration-300 hover:bg-amber-600 hover:text-white">
                      Learn More
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button href="/services" size="lg">
            View All Services
          </Button>
        </div>
      </div>

      {/* Service Modal */}
      <ServiceModal
        service={selectedService}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </section>
  );
};

export default ServicesSection;
