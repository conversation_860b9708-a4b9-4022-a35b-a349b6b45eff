'use client';

import { useState } from 'react';
import Image from 'next/image';
import { brandingImages } from '@/utils/imageUtils';

interface LogoProps {
  variant?: 'default' | 'white';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
}

export default function Logo({ 
  variant = 'default', 
  size = 'md', 
  className = '',
  showText = true 
}: LogoProps) {
  const [hasError, setHasError] = useState(false);

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl'
  };

  const logoSrc = variant === 'white' ? brandingImages.logoWhite : brandingImages.logo;

  // Fallback to text-based logo if image fails to load
  if (hasError || !logoSrc) {
    return (
      <div className={`flex items-center ${className}`}>
        <div className={`${sizeClasses[size]} bg-amber-600 rounded-lg flex items-center justify-center mr-2`}>
          <span className="text-white font-bold text-sm">P</span>
        </div>
        {showText && (
          <span className={`${textSizeClasses[size]} font-bold ${variant === 'white' ? 'text-white' : 'text-amber-600'}`}>
            Pavileo
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      <div className={`relative ${sizeClasses[size]} mr-2`}>
        <Image
          src={logoSrc}
          alt="Pavileo Logo"
          fill
          className="object-contain"
          onError={() => setHasError(true)}
          priority
        />
      </div>
      {showText && (
        <span className={`${textSizeClasses[size]} font-bold ${variant === 'white' ? 'text-white' : 'text-amber-600'}`}>
          Pavileo
        </span>
      )}
    </div>
  );
}
