'use client';

import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import Button from '@/components/ui/Button';
import ServiceModal from '@/components/ui/ServiceModal';
import { fadeInUp, textReveal, staggerAnimation } from '@/utils/gsapAnimations';

interface HeroData {
  title: string;
  subtitle: string;
  backgroundVideo?: string;
  backgroundImage?: string;
  ctaButtons: {
    primary: { text: string; href: string };
    secondary: { text: string; href: string };
  };
}

const defaultHeroData: HeroData = {
  title: "Building Excellence in Varanasi",
  subtitle: "Transform your vision into reality with Pavileo Construction. We create stunning residential, commercial, and institutional spaces that stand the test of time.",
  backgroundVideo: "https://assets.mixkit.co/videos/preview/mixkit-construction-of-a-building-in-the-city-11099-large.mp4",
  backgroundImage: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2076&q=80",
  ctaButtons: {
    primary: { text: "Explore Our Work", href: "/projects" },
    secondary: { text: "Get Free Quote", href: "/contact" }
  }
};

// Feature cards data for the hero section
const heroFeatures = [
  {
    _id: 'hero-1',
    title: 'Residential Projects',
    description: 'Custom homes & apartments',
    icon: 'home',
    image: 'https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Custom home design and construction',
      'Luxury villas and bungalows',
      'Apartment buildings and complexes',
      'Affordable housing solutions',
      'Turnkey residential projects'
    ],
    detailedDescription: 'We specialize in creating dream homes that reflect your unique style and needs. From luxury villas to affordable housing, our residential construction services cover every aspect of home building with attention to detail and quality craftsmanship.',
    bgColor: 'bg-amber-500',
    iconSvg: (
      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    )
  },
  {
    _id: 'hero-2',
    title: 'Commercial Buildings',
    description: 'Offices & retail spaces',
    icon: 'building',
    image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Office buildings and corporate headquarters',
      'Retail spaces and shopping centers',
      'Hotels and hospitality projects',
      'Educational institutions',
      'Industrial facilities'
    ],
    detailedDescription: 'Our commercial construction expertise spans across various sectors, delivering functional and aesthetically pleasing business environments that enhance productivity and customer experience.',
    bgColor: 'bg-blue-500',
    iconSvg: (
      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    )
  },
  {
    _id: 'hero-3',
    title: 'Healthcare Facilities',
    description: 'Hospitals & medical centers',
    icon: 'heart',
    image: 'https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    features: [
      'Modern hospital construction',
      'Medical center development',
      'Specialized treatment facilities',
      'Emergency care centers',
      'Rehabilitation facilities'
    ],
    detailedDescription: 'We build state-of-the-art healthcare facilities that meet the highest standards for patient care, safety, and medical functionality while creating healing environments.',
    bgColor: 'bg-green-500',
    iconSvg: (
      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
    )
  }
];

const Hero = () => {
  const [heroData, setHeroData] = useState<HeroData>(defaultHeroData);
  const [loading, setLoading] = useState(true);
  const [selectedFeature, setSelectedFeature] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchHeroData = async () => {
      try {
        // In a real app, you would fetch this from an API
        // For now, we'll use the default data
        setHeroData(defaultHeroData);
      } catch (error) {
        console.error('Error fetching hero data:', error);
        setHeroData(defaultHeroData);
      } finally {
        setLoading(false);
      }
    };

    fetchHeroData();
  }, []);

  // GSAP animations
  useEffect(() => {
    if (!loading && titleRef.current && subtitleRef.current && buttonsRef.current) {
      // Create timeline for sequential animations
      const tl = gsap.timeline();

      // Set initial states
      gsap.set([titleRef.current, subtitleRef.current, buttonsRef.current], {
        opacity: 0,
        y: 50
      });

      // Animate elements in sequence
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out"
      })
      .to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.5")
      .to(buttonsRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "back.out(1.7)"
      }, "-=0.3");

      // Add floating animation to the entire hero content
      gsap.to(heroRef.current, {
        y: -10,
        duration: 3,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1
      });
    }
  }, [loading]);

  // Modal handlers
  const handleFeatureClick = (feature: any) => {
    setSelectedFeature(feature);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedFeature(null);
  };

  return (
    <section className="relative h-screen flex items-center overflow-hidden">
      {/* Background Video */}
      <div className="absolute inset-0 z-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          preload="auto"
          poster={heroData.backgroundImage}
          className="absolute min-w-full min-h-full object-cover scale-105"
        >
          <source
            src={heroData.backgroundVideo}
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/30"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 z-5">
        <div className="absolute top-20 right-20 w-32 h-32 bg-amber-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-32 left-16 w-24 h-24 bg-blue-500/20 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-1/3 w-16 h-16 bg-green-500/20 rounded-full blur-md animate-pulse delay-2000"></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Content */}
          <div ref={heroRef} className="space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center px-4 py-2 bg-amber-600/20 backdrop-blur-sm rounded-full border border-amber-500/30">
                <span className="text-amber-400 text-sm font-medium">🏗️ Premium Construction Services</span>
              </div>

              <h1
                ref={titleRef}
                className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight"
              >
                {heroData.title.split(' ').map((word, index) => (
                  <span key={index} className={index === heroData.title.split(' ').length - 1 ? 'text-amber-400' : ''}>
                    {word}{' '}
                  </span>
                ))}
              </h1>

              <p
                ref={subtitleRef}
                className="text-lg md:text-xl text-gray-300 leading-relaxed max-w-2xl"
              >
                {heroData.subtitle}
              </p>
            </div>

            <div
              ref={buttonsRef}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button
                href={heroData.ctaButtons.primary.href}
                size="lg"
                className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                {heroData.ctaButtons.primary.text}
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Button>
              <Button
                href={heroData.ctaButtons.secondary.href}
                variant="outline"
                size="lg"
                className="text-white border-2 border-white/30 hover:bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-semibold hover:border-amber-400 hover:text-amber-400 transition-all duration-300"
              >
                {heroData.ctaButtons.secondary.text}
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 border-t border-white/20">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-amber-400">500+</div>
                <div className="text-sm text-gray-400">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-amber-400">25+</div>
                <div className="text-sm text-gray-400">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-amber-400">1000+</div>
                <div className="text-sm text-gray-400">Happy Clients</div>
              </div>
            </div>
          </div>

          {/* Right Content - Feature Cards */}
          <div className="hidden lg:block space-y-6">
            {heroFeatures.map((feature, index) => (
              <div
                key={feature._id}
                className="bg-white/10 backdrop-blur-md rounded-2xl overflow-hidden border border-white/20 hover:bg-white/20 transition-all duration-300 cursor-pointer group"
                onClick={() => handleFeatureClick(feature)}
              >
                <div className="flex items-center">
                  {/* Image Section */}
                  <div className="relative w-24 h-20 flex-shrink-0 overflow-hidden rounded-l-2xl">
                    <img
                      src={feature.image}
                      alt={feature.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent to-black/30"></div>
                  </div>

                  {/* Content Section */}
                  <div className="flex-1 p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 ${feature.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        {feature.iconSvg}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-white font-semibold group-hover:text-amber-300 transition-colors duration-300">
                          {feature.title}
                        </h3>
                        <p className="text-gray-300 text-sm">
                          {feature.description}
                        </p>
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-5 h-5 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="flex flex-col items-center space-y-2 animate-bounce">
          <span className="text-white/70 text-sm">Scroll to explore</span>
          <svg className="w-6 h-6 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>

      {/* Feature Modal */}
      <ServiceModal
        service={selectedFeature}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </section>
  );
};

export default Hero;
