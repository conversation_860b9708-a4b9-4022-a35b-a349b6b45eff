'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaStar } from 'react-icons/fa';
import OptimizedImage from '@/components/ui/OptimizedImage';

interface Testimonial {
  _id: string;
  name: string;
  position: string;
  content: string;
  rating: number;
  image?: string;
}

const defaultTestimonials = [
  {
    _id: '1',
    name: '<PERSON><PERSON>',
    position: 'Homeowner',
    content: '<PERSON><PERSON><PERSON> transformed our dream home into reality. Their attention to detail and quality of work exceeded our expectations. Highly recommended!',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    _id: '2',
    name: '<PERSON><PERSON>',
    position: 'Business Owner',
    content: 'We hired <PERSON><PERSON><PERSON> for our office renovation and they delivered exceptional results. Professional team, on-time completion, and excellent craftsmanship.',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    _id: '3',
    name: 'Amit Verma',
    position: 'Property Developer',
    content: 'Working with Pavileo on our apartment complex was a great experience. Their expertise and project management skills are outstanding.',
    rating: 4,
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    _id: '4',
    name: 'Dr. Sunita Verma',
    position: 'Hospital Administrator',
    content: 'Pavileo constructed our medical facility with precision and care. They understood the unique requirements of healthcare infrastructure perfectly. Excellent work!',
    rating: 5,
    image: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
  {
    _id: '5',
    name: 'Prof. Vikash Gupta',
    position: 'School Principal',
    content: 'The educational facility they built for our school is outstanding. Child-friendly design with modern amenities. Students and teachers love the new building.',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
  },
];

const TestimonialsSection = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/testimonials');
        if (response.ok) {
          const data = await response.json();
          setTestimonials(data);
        } else {
          // If API fails, use default testimonials
          setTestimonials(defaultTestimonials);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        setTestimonials(defaultTestimonials);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  // Use default testimonials if API call is still loading
  const displayTestimonials = loading ? defaultTestimonials : testimonials.length > 0 ? testimonials : defaultTestimonials;

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % displayTestimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [displayTestimonials.length]);

  return (
    <section className="py-16 md:py-24 bg-amber-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            What Our <span className="text-amber-600">Clients Say</span>
          </motion.h2>

          <motion.p
            className="text-gray-700"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Hear from our satisfied clients about their experience working with Pavileo.
          </motion.p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {displayTestimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial._id}
                className={`bg-white p-8 md:p-10 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ${
                  index === activeIndex ? 'block' : 'hidden'
                }`}
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{
                  opacity: index === activeIndex ? 1 : 0,
                  scale: index === activeIndex ? 1 : 0.8,
                  y: index === activeIndex ? 0 : 50
                }}
                transition={{
                  duration: 0.6,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <motion.div
                    className="flex-shrink-0"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{
                      duration: 0.6,
                      delay: 0.2,
                      type: "spring",
                      stiffness: 200
                    }}
                  >
                    <div className="relative w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden ring-4 ring-amber-100 shadow-lg">
                      <OptimizedImage
                        src={testimonial.image || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'}
                        alt={testimonial.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </motion.div>

                  <div className="flex-1 text-center md:text-left">
                    <motion.div
                      className="flex justify-center md:justify-start mb-4"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: 0.3 }}
                    >
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{
                            duration: 0.3,
                            delay: 0.4 + i * 0.1,
                            type: "spring",
                            stiffness: 300
                          }}
                        >
                          <FaStar
                            className={`${i < testimonial.rating ? 'text-amber-500' : 'text-gray-300'} hover:scale-110 transition-transform`}
                          />
                        </motion.div>
                      ))}
                    </motion.div>

                    <motion.p
                      className="text-gray-700 mb-6 italic text-lg leading-relaxed"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.5 }}
                    >
                      "{testimonial.content}"
                    </motion.p>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.6 }}
                    >
                      <h4 className="text-lg font-bold text-gray-900">{testimonial.name}</h4>
                      <p className="text-amber-600 font-medium">{testimonial.position}</p>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            className="flex justify-center mt-8 space-x-3"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            {displayTestimonials.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-4 h-4 rounded-full transition-all duration-300 ${
                  index === activeIndex ? 'bg-amber-600 scale-125' : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: index === activeIndex ? 1.25 : 1 }}
                transition={{
                  duration: 0.3,
                  delay: 0.8 + index * 0.1,
                  type: "spring",
                  stiffness: 300
                }}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
