'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { companyImages, getRandomImages } from '@/utils/imageUtils';
import { FaAward, FaCertificate, FaUsers, FaBuilding } from 'react-icons/fa';

interface GalleryItem {
  _id: string;
  title: string;
  description: string;
  image: string;
  category: 'office' | 'team' | 'awards' | 'certificates' | 'projects';
  icon?: React.ReactNode;
}

const defaultGalleryItems: GalleryItem[] = [
  {
    _id: '1',
    title: 'Our Modern Office',
    description: 'State-of-the-art facilities where innovation meets comfort',
    image: companyImages.office,
    category: 'office',
    icon: <FaBuilding />
  },
  {
    _id: '2',
    title: 'Expert Team',
    description: 'Skilled professionals dedicated to excellence',
    image: companyImages.team,
    category: 'team',
    icon: <FaUsers />
  },
  {
    _id: '3',
    title: 'Industry Awards',
    description: 'Recognition for outstanding construction quality',
    image: companyImages.awards,
    category: 'awards',
    icon: <FaAward />
  },
  {
    _id: '4',
    title: 'Certifications',
    description: 'Licensed and certified for all construction services',
    image: companyImages.certificates,
    category: 'certificates',
    icon: <FaCertificate />
  }
];

export default function CompanyGallerySection() {
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'office', label: 'Office' },
    { id: 'team', label: 'Team' },
    { id: 'awards', label: 'Awards' },
    { id: 'certificates', label: 'Certificates' }
  ];

  useEffect(() => {
    const loadGalleryItems = async () => {
      try {
        // In a real app, this would be an API call
        // const response = await fetch('/api/gallery');
        // const data = await response.json();
        
        // Add some additional project images
        const projectImages = getRandomImages('construction', 4);
        const additionalItems: GalleryItem[] = projectImages.map((image, index) => ({
          _id: `project-${index + 1}`,
          title: `Project ${index + 1}`,
          description: 'Quality construction delivered on time',
          image,
          category: 'projects' as const,
          icon: <FaBuilding />
        }));
        
        setGalleryItems([...defaultGalleryItems, ...additionalItems]);
      } catch (error) {
        console.error('Error loading gallery items:', error);
        setGalleryItems(defaultGalleryItems);
      } finally {
        setLoading(false);
      }
    };

    loadGalleryItems();
  }, []);

  const filteredItems = selectedCategory === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory);

  if (loading) {
    return (
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-64 animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Company <span className="text-amber-600">Gallery</span>
          </motion.h2>
          <motion.p
            className="text-gray-700 text-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Explore our workspace, team, achievements, and the quality that defines Pavileo.
          </motion.p>
        </div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-amber-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.label}
            </button>
          ))}
        </motion.div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item._id}
              className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              {/* Image */}
              <div className="relative h-64 overflow-hidden">
                <OptimizedImage
                  src={item.image}
                  alt={item.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                  category={item.category === 'projects' ? 'construction' : item.category}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                
                {/* Icon */}
                <div className="absolute top-4 right-4 text-white text-2xl opacity-80">
                  {item.icon}
                </div>
              </div>

              {/* Content */}
              <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                <p className="text-gray-200 text-sm">{item.description}</p>
              </div>

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-amber-600/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="text-3xl mb-2">{item.icon}</div>
                  <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                  <p className="text-sm">{item.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <p className="text-gray-700 mb-6">
            Want to see more of our work? Visit our projects page or contact us for a consultation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/projects"
              className="bg-amber-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-amber-700 transition-colors"
            >
              View All Projects
            </a>
            <a
              href="/contact"
              className="border border-amber-600 text-amber-600 px-8 py-3 rounded-lg font-medium hover:bg-amber-600 hover:text-white transition-colors"
            >
              Get In Touch
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
