'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaArrowLeft, FaEnvelope, FaUser, FaCalendarAlt, FaReply } from 'react-icons/fa';

interface Message {
  id: string;
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  createdAt: string;
  read: boolean;
}

export default function MessageDetail({ params }: { params: { id: string } }) {
  const [message, setMessage] = useState<Message | null>(null);
  const [loading, setLoading] = useState(true);
  const [replyMode, setReplyMode] = useState(false);
  const [replyText, setReplyText] = useState('');

  useEffect(() => {
    const fetchMessage = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        const messageData = {
          id: params.id,
          name: '<PERSON><PERSON><PERSON>',
          email: '<EMAIL>',
          phone: '+91 98765 43210',
          subject: 'New Home Construction Inquiry',
          message: 'I am interested in building a new home in Varanasi. I would like to schedule a consultation to discuss my requirements and get a quote.\n\nI am looking for a 3-bedroom villa with modern amenities and would like to understand the timeline and budget for such a project.\n\nPlease let me know your availability for a meeting or call to discuss this further.\n\nThank you,\nVikram Singh',
          createdAt: '2023-05-01T10:30:00Z',
          read: true,
        };
        
        setMessage(messageData);
      } catch (error) {
        console.error('Error fetching message:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMessage();
  }, [params.id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleReply = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real application, you would call your API to send the reply
    alert(`Reply sent to ${message?.email}`);
    setReplyMode(false);
    setReplyText('');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (!message) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Message Not Found</h2>
        <p className="text-gray-600 mb-6">The message you are looking for does not exist or has been removed.</p>
        <Link 
          href="/admin/messages" 
          className="text-amber-600 hover:text-amber-800 flex items-center justify-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Messages
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Link 
          href="/admin/messages" 
          className="text-amber-600 hover:text-amber-800 mr-4"
        >
          <FaArrowLeft />
        </Link>
        <h1 className="text-2xl font-bold">Message Details</h1>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold mb-2">{message.subject}</h2>
          <div className="flex items-center text-gray-600 text-sm">
            <FaCalendarAlt className="mr-2" />
            <span>{formatDate(message.createdAt)}</span>
          </div>
        </div>
        
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-start mb-4">
            <div className="bg-gray-200 rounded-full w-10 h-10 flex items-center justify-center mr-4">
              <FaUser className="text-gray-600" />
            </div>
            <div>
              <h3 className="font-bold text-gray-900">{message.name}</h3>
              <div className="flex items-center text-gray-600 text-sm">
                <FaEnvelope className="mr-2" />
                <a href={`mailto:${message.email}`} className="hover:text-amber-600">
                  {message.email}
                </a>
              </div>
              {message.phone && (
                <div className="text-gray-600 text-sm mt-1">
                  Phone: {message.phone}
                </div>
              )}
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg whitespace-pre-line">
            {message.message}
          </div>
        </div>
        
        <div className="p-6">
          {!replyMode ? (
            <button 
              onClick={() => setReplyMode(true)}
              className="bg-amber-600 text-white px-4 py-2 rounded-md flex items-center"
            >
              <FaReply className="mr-2" /> Reply
            </button>
          ) : (
            <form onSubmit={handleReply}>
              <h3 className="font-bold text-gray-900 mb-2">Reply to {message.name}</h3>
              <textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 mb-4"
                rows={6}
                placeholder="Type your reply here..."
                required
              ></textarea>
              <div className="flex space-x-2">
                <button 
                  type="submit"
                  className="bg-amber-600 text-white px-4 py-2 rounded-md"
                >
                  Send Reply
                </button>
                <button 
                  type="button"
                  onClick={() => setReplyMode(false)}
                  className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md"
                >
                  Cancel
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
