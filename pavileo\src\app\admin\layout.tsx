import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import AdminHeader from '@/components/admin/AdminHeader';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export const metadata: Metadata = {
  title: 'Admin Dashboard',
  description: 'Pavileo Construction Admin Dashboard',
};

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // For demo purposes, we'll skip the authentication check
  // In a real app, you would use:
  // const session = await getServerSession(authOptions);
  // if (!session) {
  //   redirect('/admin/login');
  // }

  const session = { user: { name: 'Admin User', email: '<EMAIL>', role: 'admin' } };

  return (
    <div className="min-h-screen bg-gray-100">
      <AdminHeader />
      <main className="container mx-auto px-4 py-8 pt-20">
        {children}
      </main>
    </div>
  );
}
