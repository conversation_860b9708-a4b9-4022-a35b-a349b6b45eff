'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import OptimizedImage from '@/components/ui/OptimizedImage';
import ProjectModal from '@/components/ui/ProjectModal';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { fadeInUp, staggerAnimation, scaleIn, hoverLift } from '@/utils/gsapAnimations';
import { getProjectImage, getImageWithFallback } from '@/utils/imageUtils';

interface Project {
  _id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  images: string[];
  completionDate?: string;
  client?: string;
  detailedDescription?: string;
  features?: string[];
}

const defaultProjects = [
  {
    _id: '1',
    title: 'Modern Villa',
    description: 'Luxury villa with contemporary design and premium finishes.',
    category: 'Residential',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '2',
    title: 'Commercial Complex',
    description: 'Multi-story commercial building with modern amenities.',
    category: 'Commercial',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '3',
    title: 'Apartment Building',
    description: 'Residential apartment complex with 24 luxury units.',
    category: 'Residential',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-**********-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80'],
  },
  {
    _id: '4',
    title: 'City Hospital',
    description: 'State-of-the-art medical facility with 200 beds and modern equipment.',
    category: 'Healthcare',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80'],
  },
  {
    _id: '5',
    title: 'Specialty Medical Center',
    description: 'Advanced cardiac and cancer treatment center with cutting-edge technology.',
    category: 'Healthcare',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1632833239869-a37e3a5806d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80'],
  },
  {
    _id: '6',
    title: 'Wellness Hospital',
    description: 'Comprehensive healthcare facility with rehabilitation and wellness center.',
    category: 'Healthcare',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2053&q=80'],
  },
  {
    _id: '7',
    title: 'International School',
    description: 'Modern educational campus with smart classrooms and sports facilities.',
    category: 'Educational',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-**********707-520aed937b7b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2532&q=80'],
  },
  {
    _id: '8',
    title: 'University Campus',
    description: 'Multi-building university complex with library, labs, and dormitories.',
    category: 'Educational',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-**********-701939374585?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2532&q=80'],
  },
  {
    _id: '9',
    title: 'Technical Institute',
    description: 'Vocational training center with workshops and modern equipment.',
    category: 'Educational',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '10',
    title: 'Primary School',
    description: 'Child-friendly school building with playground and activity areas.',
    category: 'Educational',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '11',
    title: 'Medical Center',
    description: 'Comprehensive medical center with diagnostic facilities and emergency care.',
    category: 'Healthcare',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '12',
    title: 'University Library',
    description: 'Modern library building with study halls and digital resources.',
    category: 'Educational',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '13',
    title: 'Shopping Mall',
    description: 'Multi-level shopping complex with retail stores and entertainment.',
    category: 'Commercial',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '14',
    title: 'Luxury Townhouse',
    description: 'Premium townhouse development with private gardens and modern amenities.',
    category: 'Residential',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
  {
    _id: '15',
    title: 'Office Tower',
    description: 'High-rise office building with modern facilities and parking.',
    category: 'Commercial',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
  },
];

const ProjectsSection = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const categories = ['All', 'Residential', 'Commercial', 'Healthcare', 'Educational'];

  // Refs for GSAP animations
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await fetch('/api/projects');
        if (response.ok) {
          const data = await response.json();
          setProjects(data);
          setFilteredProjects(data.slice(0, 6)); // Show first 6 projects initially
        } else {
          // If API fails, use default projects
          setProjects(defaultProjects);
          setFilteredProjects(defaultProjects.slice(0, 6));
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        setProjects(defaultProjects);
        setFilteredProjects(defaultProjects.slice(0, 6));
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Filter projects when category changes
  useEffect(() => {
    if (projects.length > 0) {
      const filtered = selectedCategory === 'All'
        ? projects
        : projects.filter(project => project.category === selectedCategory);
      setFilteredProjects(filtered.slice(0, 6)); // Limit to 6 projects
    }
  }, [selectedCategory, projects]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  // Modal handlers
  const handleProjectClick = (project: Project) => {
    setSelectedProject(project);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedProject(null);
  };

  // GSAP animations
  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }

    // Animate section elements on scroll
    if (titleRef.current && subtitleRef.current && filtersRef.current) {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });

      tl.fromTo(titleRef.current,
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }
      )
      .fromTo(subtitleRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
        "-=0.4"
      )
      .fromTo(filtersRef.current.children,
        { y: 20, opacity: 0, scale: 0.8 },
        { y: 0, opacity: 1, scale: 1, duration: 0.5, stagger: 0.1, ease: "back.out(1.7)" },
        "-=0.2"
      );
    }
  }, []);

  // Animate project cards when they change
  useEffect(() => {
    if (gridRef.current && !loading) {
      const cards = gridRef.current.querySelectorAll('.project-card');

      gsap.fromTo(cards,
        { y: 50, opacity: 0, scale: 0.8 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: gridRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Add hover effects to cards
      cards.forEach((card) => {
        const cardElement = card as HTMLElement;

        cardElement.addEventListener('mouseenter', () => {
          gsap.to(cardElement, {
            y: -10,
            scale: 1.02,
            boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
            duration: 0.3,
            ease: "power2.out"
          });
        });

        cardElement.addEventListener('mouseleave', () => {
          gsap.to(cardElement, {
            y: 0,
            scale: 1,
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
            duration: 0.3,
            ease: "power2.out"
          });
        });
      });
    }
  }, [filteredProjects, loading]);

  // Use filtered projects for display
  const displayProjects = loading ? defaultProjects.slice(0, 6) : filteredProjects;

  return (
    <section ref={sectionRef} className="py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2
            ref={titleRef}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            Our <span className="text-amber-600">Projects</span>
          </h2>

          <p
            ref={subtitleRef}
            className="text-gray-700 mb-8"
          >
            Explore our construction projects that showcase our expertise and quality workmanship.
          </p>

          {/* Category Filter */}
          <div
            ref={filtersRef}
            className="flex flex-wrap justify-center gap-3 mb-8"
          >
            {categories.map((category, index) => (
              <button
                key={category}
                onClick={() => handleCategoryChange(category)}
                className={`filter-button px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                  category === selectedCategory
                    ? 'bg-amber-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-amber-50 hover:text-amber-600 shadow-md'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        <div ref={gridRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayProjects.map((project, index) => (
            <div
              key={project._id}
              className="project-card bg-white rounded-lg overflow-hidden shadow-md transition-all duration-300 group cursor-pointer"
              onClick={() => handleProjectClick(project)}
            >
                <div className="relative h-64 overflow-hidden">
                  <div className="w-full h-full">
                    <OptimizedImage
                      src={getImageWithFallback(project.images[0], project.category.toLowerCase() as any)}
                      alt={project.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-110"
                      category={project.category.toLowerCase() as any}
                    />
                  </div>

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Category Badge */}
                  <div className="absolute top-4 right-4">
                    <span className="bg-amber-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                      {project.category}
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 group-hover:text-amber-600 transition-colors duration-300">
                    {project.title}
                  </h3>

                  <p className="text-gray-700 mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-gray-600 text-sm">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      {project.location}
                    </div>

                    <span className="text-amber-600 font-medium flex items-center group-hover:text-amber-700 transition-colors">
                      View Details
                      <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button href="/projects" size="lg">
            View All Projects
          </Button>
        </div>
      </div>

      {/* Project Modal */}
      <ProjectModal
        project={selectedProject}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </section>
  );
};

export default ProjectsSection;
