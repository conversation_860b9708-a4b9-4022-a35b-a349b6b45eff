import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Mock data for demo purposes
const mockServices = [
  {
    _id: '1',
    title: 'Residential Construction',
    description: 'Custom homes built to your specifications with quality materials and expert craftsmanship.',
    icon: 'home',
    order: 1,
  },
  {
    _id: '2',
    title: 'Commercial Construction',
    description: 'Functional and attractive commercial spaces designed to meet your business needs.',
    icon: 'building',
    order: 2,
  },
  {
    _id: '3',
    title: 'Renovation & Remodeling',
    description: 'Transform your existing space with our comprehensive renovation services.',
    icon: 'paintRoller',
    order: 3,
  },
  {
    _id: '4',
    title: 'Interior Design',
    description: 'Professional interior design services to create beautiful and functional spaces.',
    icon: 'rulerCombined',
    order: 4,
  },
  {
    _id: '5',
    title: 'Project Management',
    description: 'End-to-end project management ensuring timely completion within budget.',
    icon: 'clipboardCheck',
    order: 5,
  },
];

export async function GET(req: NextRequest) {
  try {
    return NextResponse.json(mockServices);
  } catch (error) {
    console.error('Error fetching services:', error);
    return NextResponse.json(
      { error: 'Failed to fetch services' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // In a real app, you would check authentication and save to database
    return NextResponse.json(
      { message: 'Service created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating service:', error);
    return NextResponse.json(
      { error: 'Failed to create service' },
      { status: 500 }
    );
  }
}
