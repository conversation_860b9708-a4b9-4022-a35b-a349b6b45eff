import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Mock data for demo purposes
const mockProjects = [
  {
    _id: '1',
    title: 'Modern Villa',
    description: 'Luxury villa with contemporary design and premium finishes.',
    category: 'Residential',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80'],
    featured: true,
  },
  {
    _id: '2',
    title: 'Commercial Complex',
    description: 'Multi-story commercial building with modern amenities.',
    category: 'Commercial',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80'],
    featured: true,
  },
  {
    _id: '3',
    title: 'Apartment Building',
    description: 'Residential apartment complex with 24 luxury units.',
    category: 'Residential',
    location: 'Varanasi',
    images: ['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80'],
    featured: true,
  },
];

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const featured = searchParams.get('featured');
    const category = searchParams.get('category');
    const limit = searchParams.get('limit');

    let filteredProjects = [...mockProjects];

    if (featured === 'true') {
      filteredProjects = filteredProjects.filter(project => project.featured);
    }

    if (category) {
      filteredProjects = filteredProjects.filter(project => project.category === category);
    }

    if (limit) {
      filteredProjects = filteredProjects.slice(0, parseInt(limit));
    }

    return NextResponse.json(filteredProjects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // In a real app, you would check authentication and save to database
    return NextResponse.json(
      { message: 'Project created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
