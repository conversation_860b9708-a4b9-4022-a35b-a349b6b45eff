'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaArrowLeft, FaEdit } from 'react-icons/fa';

interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  category: string;
  location: string;
  completionDate: string;
  client: string;
  images: string[];
  features: string[];
  challenges: string[];
  solutions: string[];
  featured: boolean;
}

export default function ProjectDetail({ params }: { params: { id: string } }) {
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProject = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        const projectData = {
          id: params.id,
          title: 'Modern Villa',
          description: 'Luxury villa with contemporary design and premium finishes.',
          longDescription: 'This modern villa project showcases our commitment to excellence in residential construction. The 5,000 square foot property features 5 bedrooms, 6 bathrooms, a home theater, swimming pool, and landscaped gardens. We used premium materials throughout, including Italian marble, hardwood flooring, and custom cabinetry. The project was completed in 12 months, delivering a stunning contemporary home that perfectly balances luxury, functionality, and comfort.',
          category: 'Residential',
          location: 'Varanasi',
          completionDate: 'January 2023',
          client: 'Private Owner',
          images: [
            'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
            'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
            'https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
          ],
          features: [
            '5,000 square feet of living space',
            '5 bedrooms and 6 bathrooms',
            'Home theater and entertainment area',
            'Swimming pool and landscaped gardens',
            'Smart home automation system',
          ],
          challenges: [
            'Complex architectural design requiring precise execution',
            'Integration of smart home technology throughout the property',
            'Custom-designed elements requiring specialized craftsmanship',
            'Strict timeline to meet client\'s move-in date',
          ],
          solutions: [
            'Assembled a specialized team with expertise in luxury home construction',
            'Collaborated with technology specialists for seamless smart home integration',
            'Partnered with skilled artisans for custom elements',
            'Implemented detailed project management to ensure timely completion',
          ],
          featured: true,
        };
        
        setProject(projectData);
      } catch (error) {
        console.error('Error fetching project:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Project Not Found</h2>
        <p className="text-gray-600 mb-6">The project you are looking for does not exist or has been removed.</p>
        <Link 
          href="/admin/projects" 
          className="text-amber-600 hover:text-amber-800 flex items-center justify-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Projects
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link 
            href="/admin/projects" 
            className="text-amber-600 hover:text-amber-800 mr-4"
          >
            <FaArrowLeft />
          </Link>
          <h1 className="text-2xl font-bold">{project.title}</h1>
        </div>
        <Link 
          href={`/admin/projects/${project.id}/edit`} 
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <FaEdit className="mr-2" /> Edit Project
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h2 className="text-lg font-semibold mb-2">Project Details</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Category</p>
                <p className="font-medium">{project.category}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Location</p>
                <p className="font-medium">{project.location}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Completion Date</p>
                <p className="font-medium">{project.completionDate}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Client</p>
                <p className="font-medium">{project.client}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Featured</p>
                <p className="font-medium">{project.featured ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">Description</h2>
            <p className="text-gray-700">{project.description}</p>
          </div>
        </div>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Long Description</h2>
          <p className="text-gray-700">{project.longDescription}</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h2 className="text-lg font-semibold mb-2">Features</h2>
            <ul className="list-disc pl-5 space-y-1">
              {project.features.map((feature, index) => (
                <li key={index} className="text-gray-700">{feature}</li>
              ))}
            </ul>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">Challenges</h2>
            <ul className="list-disc pl-5 space-y-1">
              {project.challenges.map((challenge, index) => (
                <li key={index} className="text-gray-700">{challenge}</li>
              ))}
            </ul>
          </div>
        </div>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Solutions</h2>
          <ul className="list-disc pl-5 space-y-1">
            {project.solutions.map((solution, index) => (
              <li key={index} className="text-gray-700">{solution}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h2 className="text-lg font-semibold mb-4">Project Images</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {project.images.map((image, index) => (
              <div key={index} className="relative h-48 rounded-lg overflow-hidden">
                <Image
                  src={image}
                  alt={`${project.title} - Image ${index + 1}`}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
