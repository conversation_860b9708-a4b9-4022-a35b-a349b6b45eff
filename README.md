# Pavileo Construction Website

A modern, responsive website for Pavileo Construction Company based in Varanasi, built with Next.js 15, MongoDB Atlas, and featuring an admin panel for content management.

## Features

- Responsive design for all devices (mobile, tablet, desktop)
- Dynamic content management through MongoDB
- Admin panel for managing projects, services, testimonials, and contact messages
- Authentication system for admin users
- Modern UI with animations and transitions

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB Atlas
- **Authentication**: NextAuth.js
- **UI Libraries**: Framer Motion, React Icons, Headless UI
- **Form Handling**: React Hook Form

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- MongoDB Atlas account

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd pavileo
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env.local` file in the root directory with the following variables:
   ```
   # MongoDB
   MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority

   # NextAuth
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here

   # Admin User (for initial setup)
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=your-secure-password
   ```

4. Initialize the admin user:
   ```bash
   node src/scripts/init-admin.js
   ```

5. Run the development server:
   ```bash
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the website.

## Project Structure

```
pavileo/
├── public/            # Static assets
├── src/
│   ├── app/           # Next.js App Router
│   │   ├── admin/     # Admin panel pages
│   │   ├── api/       # API routes
│   │   └── ...        # Public pages
│   ├── components/    # React components
│   │   ├── admin/     # Admin components
│   │   ├── home/      # Home page components
│   │   ├── layout/    # Layout components
│   │   ├── providers/ # Context providers
│   │   └── ui/        # UI components
│   ├── lib/           # Utility functions
│   ├── models/        # MongoDB models
│   ├── scripts/       # Utility scripts
│   └── types/         # TypeScript type definitions
├── .env.local.example # Environment variables example
└── ...                # Config files
```

## Admin Panel

The admin panel is accessible at `/admin` and requires authentication. Use the credentials specified in your `.env.local` file to log in.

### Admin Features

- Dashboard with overview statistics
- Project management (add, edit, delete)
- Service management
- Testimonial management
- Contact message management

## Deployment

This project can be deployed to Vercel, Netlify, or any other hosting platform that supports Next.js.

### Deploying to Vercel

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket)
2. Import the project to Vercel
3. Set the environment variables in the Vercel dashboard
4. Deploy

## License

This project is licensed under the MIT License.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
- [Tailwind CSS](https://tailwindcss.com/)
- [Framer Motion](https://www.framer.com/motion/)
- [NextAuth.js](https://next-auth.js.org/)
