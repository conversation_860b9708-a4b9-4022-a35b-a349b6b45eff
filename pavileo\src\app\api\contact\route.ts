import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Mock data for demo purposes
const mockContacts = [
  {
    _id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+919876543210',
    message: 'I would like to inquire about your residential construction services.',
    status: 'new',
    createdAt: new Date('2023-06-15').toISOString(),
  },
  {
    _id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+919876543211',
    message: 'Can you provide a quote for renovating my kitchen?',
    status: 'read',
    createdAt: new Date('2023-06-10').toISOString(),
  },
  {
    _id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+919876543212',
    message: 'I am interested in your commercial construction services for my new office space.',
    status: 'new',
    createdAt: new Date('2023-06-05').toISOString(),
  },
];

export async function GET(req: NextRequest) {
  try {
    // In a real app, you would check authentication
    return NextResponse.json(mockContacts);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contacts' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // In a real app, you would save to database
    return NextResponse.json(
      { message: 'Message sent successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating contact:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}
