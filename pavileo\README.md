# Pavileo Construction Website

A professional website for Pavileo Construction company built with Next.js, MongoDB, and Tailwind CSS.

## Features

- Responsive design for all devices
- Admin dashboard for content management
- Dynamic services and projects sections
- Contact form with database integration
- Testimonials section
- Authentication for admin users

## Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB Atlas
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **Icons**: React Icons

## Getting Started

### Prerequisites

- Node.js 18.17.0 or later
- MongoDB Atlas account
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/pavileo.git
   cd pavileo
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   - Copy `.env.local.example` to `.env.local`
   - Update the values in `.env.local` with your MongoDB URI and other settings

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Deployment

### Vercel (Recommended)

1. Create a Vercel account at [vercel.com](https://vercel.com)
2. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```
3. Login to Vercel:
   ```bash
   vercel login
   ```
4. Deploy:
   ```bash
   vercel
   ```
5. For production deployment:
   ```bash
   vercel --prod
   ```

### Environment Variables for Production

Make sure to set these environment variables in your Vercel project settings:

- `MONGODB_URI`: Your MongoDB connection string
- `NEXTAUTH_URL`: Your production domain (e.g., https://yourdomain.com)
- `NEXTAUTH_SECRET`: A secure random string for JWT encryption
- `ADMIN_EMAIL`: Admin email for login
- `ADMIN_PASSWORD`: Admin password for login

## Admin Access

Access the admin dashboard at `/admin/login` with these credentials:
- Email: <EMAIL>
- Password: admin123

## Project Structure

```
pavileo/
├── public/             # Static files
├── src/
│   ├── app/            # Next.js app router
│   │   ├── (public)/   # Public routes
│   │   ├── admin/      # Admin routes
│   │   └── api/        # API routes
│   ├── components/     # React components
│   ├── lib/            # Utility functions
│   ├── models/         # MongoDB models
│   └── types/          # TypeScript type definitions
├── .env.local          # Environment variables (development)
├── .env.production     # Environment variables (production)
└── next.config.ts      # Next.js configuration
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For any inquiries, please contact <NAME_EMAIL>
