import mongoose, { Schema, models } from 'mongoose';

const SectionSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Title is required'],
      trim: true,
    },
    subtitle: {
      type: String,
      trim: true,
    },
    content: {
      type: String,
    },
    image: {
      type: String,
    },
    type: {
      type: String,
      required: [true, 'Type is required'],
      enum: ['hero', 'about', 'services', 'projects', 'testimonials', 'contact', 'custom'],
    },
    position: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    settings: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

export default models.Section || mongoose.model('Section', SectionSchema);
