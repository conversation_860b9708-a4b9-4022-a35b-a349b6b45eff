/**
 * Image utility functions for dynamic image management
 * Provides fallback images and dynamic image loading
 */

export interface ImageConfig {
  src: string;
  alt: string;
  fallback?: string;
  category?: string;
}

// Construction-related image categories with high-quality Unsplash images
export const imageCategories = {
  // Hero/Banner Images
  hero: [
    'https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2076&q=80',
    'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80',
    'https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ],
  
  // Residential Construction
  residential: [
    'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2073&q=80',
    'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80',
    'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ],
  
  // Commercial Construction
  commercial: [
    'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=2096&q=80',
    'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80',
  ],
  
  // Interior Design
  interior: [
    'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2058&q=80',
    'https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
    'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1600566752355-35792bedcfea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ],
  
  // Construction Process
  construction: [
    'https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1632833239869-a37e3a5806d2?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ],
  
  // Team/People
  team: [
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80',
    'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80',
  ],
  
  // Architecture/Planning
  architecture: [
    'https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2031&q=80',
    'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ],
  
  // Tools/Equipment
  tools: [
    'https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2076&q=80',
    'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80',
    'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ],
  
  // Default fallback
  default: [
    'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2073&q=80',
    'https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  ]
};

/**
 * Get a random image from a specific category
 */
export const getRandomImage = (category: keyof typeof imageCategories = 'default'): string => {
  const images = imageCategories[category] || imageCategories.default;
  return images[Math.floor(Math.random() * images.length)];
};

/**
 * Get multiple random images from a category
 */
export const getRandomImages = (
  category: keyof typeof imageCategories = 'default', 
  count: number = 3
): string[] => {
  const images = imageCategories[category] || imageCategories.default;
  const shuffled = [...images].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, images.length));
};

/**
 * Get image with fallback
 */
export const getImageWithFallback = (
  src?: string, 
  category: keyof typeof imageCategories = 'default'
): string => {
  return src || getRandomImage(category);
};

/**
 * Generate image gallery for services/projects
 */
export const generateImageGallery = (
  serviceId: string, 
  category: keyof typeof imageCategories = 'default',
  count: number = 3
): string[] => {
  // Use serviceId as seed for consistent images
  const seed = serviceId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const images = imageCategories[category] || imageCategories.default;
  
  const selectedImages: string[] = [];
  for (let i = 0; i < count && i < images.length; i++) {
    const index = (seed + i) % images.length;
    selectedImages.push(images[index]);
  }
  
  return selectedImages;
};

/**
 * Service-specific image mappings
 */
export const serviceImageMap = {
  'residential-construction': 'residential',
  'commercial-construction': 'commercial', 
  'interior-design': 'interior',
  'architecture': 'architecture',
  'renovation': 'residential',
  'consultation': 'team',
  'project-management': 'construction',
  'default': 'construction'
} as const;

/**
 * Get service category image
 */
export const getServiceImage = (serviceType: string): string => {
  const category = serviceImageMap[serviceType as keyof typeof serviceImageMap] || 'construction';
  return getRandomImage(category);
};

/**
 * Project category image mappings
 */
export const projectImageMap = {
  'residential': 'residential',
  'commercial': 'commercial',
  'interior': 'interior',
  'renovation': 'residential',
  'construction': 'construction',
  'default': 'construction'
} as const;

/**
 * Get project category image
 */
export const getProjectImage = (projectCategory: string): string => {
  const category = projectImageMap[projectCategory.toLowerCase() as keyof typeof projectImageMap] || 'construction';
  return getRandomImage(category);
};

/**
 * Logo and branding images
 */
export const brandingImages = {
  logo: '/images/logo.svg',
  logoWhite: '/images/logo-white.svg',
  favicon: '/images/favicon.svg',
  ogImage: '/images/og-image.svg',
  // Placeholder images for different categories
  placeholderHero: '/images/placeholder-hero.svg',
  placeholderAbout: '/images/placeholder-about.svg',
  placeholderService: '/images/placeholder-service.svg',
  placeholderProject: '/images/placeholder-project.svg',
  placeholderTeam: '/images/placeholder-team.svg',
  placeholderResidential: '/images/placeholder-residential.svg',
  placeholderCommercial: '/images/placeholder-commercial.svg',
  placeholderInterior: '/images/placeholder-interior.svg',
  placeholderConstruction: '/images/placeholder-construction.svg',
};

/**
 * Company images
 */
export const companyImages = {
  office: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80',
  team: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  awards: 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
  certificates: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2026&q=80',
};

/**
 * Get local placeholder image for category
 */
export const getLocalPlaceholder = (category: keyof typeof imageCategories): string => {
  const placeholderMap = {
    hero: brandingImages.placeholderHero,
    residential: brandingImages.placeholderResidential,
    commercial: brandingImages.placeholderCommercial,
    interior: brandingImages.placeholderInterior,
    construction: brandingImages.placeholderConstruction,
    team: brandingImages.placeholderTeam,
    architecture: brandingImages.placeholderConstruction,
    tools: brandingImages.placeholderConstruction,
    default: brandingImages.placeholderService,
  };

  return placeholderMap[category] || brandingImages.placeholderService;
};

/**
 * Enhanced image with fallback that prefers local placeholders
 */
export const getImageWithLocalFallback = (
  src?: string,
  category: keyof typeof imageCategories = 'default'
): string => {
  if (src) return src;

  // Try local placeholder first, then external
  return getLocalPlaceholder(category);
};

/**
 * Check if we should use local images (for development/offline)
 */
export const shouldUseLocalImages = (): boolean => {
  return process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_USE_LOCAL_IMAGES === 'true';
};

export default {
  imageCategories,
  getRandomImage,
  getRandomImages,
  getImageWithFallback,
  getImageWithLocalFallback,
  getLocalPlaceholder,
  generateImageGallery,
  getServiceImage,
  getProjectImage,
  brandingImages,
  companyImages,
  shouldUseLocalImages,
};
