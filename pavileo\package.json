{"name": "pavileo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prod:build": "NODE_ENV=production next build", "prod:start": "NODE_ENV=production next start", "analyze": "ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^2.2.2", "bcryptjs": "^3.0.2", "framer-motion": "^12.9.4", "gsap": "^3.13.0", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "next": "15.3.1", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}