import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

// Simplified auth options for demo purposes
export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        // This is a simplified demo version
        // In a real app, you would validate against your database
        if (credentials?.email === '<EMAIL>' && credentials?.password === 'admin123') {
          return {
            id: '1',
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
          };
        }

        return null;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;
      }
      return session;
    },
  },
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
