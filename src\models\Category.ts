import mongoose, { Schema, models } from 'mongoose';

const CategorySchema = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
    },
    slug: {
      type: String,
      required: [true, 'Slug is required'],
      unique: true,
      trim: true,
      lowercase: true,
    },
    description: {
      type: String,
    },
    type: {
      type: String,
      required: [true, 'Type is required'],
      enum: ['project', 'service'],
    },
  },
  {
    timestamps: true,
  }
);

export default models.Category || mongoose.model('Category', CategorySchema);
