'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import OptimizedImage from './OptimizedImage';
import { FaTimes, FaPhone, FaEnvelope, FaMapMarkerAlt, FaCheckCircle, FaStar, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { generateImageGallery, getServiceImage } from '@/utils/imageUtils';

interface DetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'learn-more';
  service: any;
}

const DetailModal: React.FC<DetailModalProps> = ({ isOpen, onClose, type, service }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Generate dynamic gallery images based on service type
  const getServiceGallery = (serviceId: string) => {
    if (!service) return [];

    // Determine category based on service title/description
    let category: 'residential' | 'commercial' | 'interior' | 'construction' | 'team' = 'construction';

    const serviceTitle = service.title.toLowerCase();
    if (serviceTitle.includes('residential') || serviceTitle.includes('home')) {
      category = 'residential';
    } else if (serviceTitle.includes('commercial') || serviceTitle.includes('office')) {
      category = 'commercial';
    } else if (serviceTitle.includes('interior') || serviceTitle.includes('design')) {
      category = 'interior';
    } else if (serviceTitle.includes('consultation') || serviceTitle.includes('team')) {
      category = 'team';
    }

    // Generate consistent gallery for this service
    const gallery = generateImageGallery(serviceId, category, 4);

    // Include the service's main image if it exists
    if (service.image) {
      return [service.image, ...gallery.slice(0, 3)];
    }

    return gallery;
  };

  const gallery = getServiceGallery(service?._id);

  useEffect(() => {
    if (isOpen && modalRef.current && overlayRef.current && contentRef.current) {
      // Animate modal opening
      gsap.set(modalRef.current, { display: 'flex' });
      gsap.fromTo(overlayRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.3, ease: "power2.out" }
      );
      gsap.fromTo(contentRef.current,
        { opacity: 0, scale: 0.8, y: 50 },
        { opacity: 1, scale: 1, y: 0, duration: 0.4, ease: "back.out(1.7)", delay: 0.1 }
      );

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else if (!isOpen && modalRef.current && overlayRef.current && contentRef.current) {
      // Animate modal closing
      gsap.to(contentRef.current, {
        opacity: 0,
        scale: 0.8,
        y: 50,
        duration: 0.3,
        ease: "power2.in"
      });
      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          if (modalRef.current) {
            gsap.set(modalRef.current, { display: 'none' });
          }
        }
      });

      // Restore body scroll
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!service) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % gallery.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + gallery.length) % gallery.length);
  };

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{ display: 'none' }}
    >
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={handleOverlayClick}
      />

      {/* Modal Content */}
      <div
        ref={contentRef}
        className="relative bg-white rounded-3xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-10 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors duration-200"
        >
          <FaTimes className="text-gray-600" />
        </button>

        {/* Modal Body */}
        <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
          {/* Image Gallery Section */}
          <div className="relative h-64 lg:h-full">
            <OptimizedImage
              src={gallery[currentImageIndex]}
              alt={service.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

            {/* Gallery Navigation */}
            {gallery.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                {/* Image Indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {gallery.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </>
            )}

            {/* Service Badge */}
            <div className="absolute top-6 left-6">
              <span className="bg-amber-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                Service Details
              </span>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8 overflow-y-auto">
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {service.title}
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  {service.detailedDescription || service.description}
                </p>
              </div>

              {/* Learn More Content */}
              {service.features && service.features.length > 0 && (
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    What We Offer:
                  </h3>
                  <ul className="space-y-3">
                    {service.features.map((feature: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <FaCheckCircle className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Why Choose Us */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Why Choose Pavileo?
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <FaStar className="w-5 h-5 text-amber-500" />
                    <span className="text-gray-700">25+ Years Experience</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FaCheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Quality Guaranteed</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FaStar className="w-5 h-5 text-amber-500" />
                    <span className="text-gray-700">Licensed & Insured</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FaCheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">On-Time Delivery</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="pt-6 border-t border-gray-200">
                <div className="flex justify-center">
                  <button
                    onClick={onClose}
                    className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
                  >
                    View Portfolio
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailModal;
