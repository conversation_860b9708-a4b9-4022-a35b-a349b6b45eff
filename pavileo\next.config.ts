import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable production optimizations
  reactStrictMode: true,

  // Configure image domains
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
      },
      {
        protocol: 'https',
        hostname: 'assets.mixkit.co',
      },
    ],
  },

  // Optimize output
  output: 'standalone',

  // Add trailing slashes for better SEO
  trailingSlash: true,

  // Increase timeout for builds
  staticPageGenerationTimeout: 120,
};

export default nextConfig;
