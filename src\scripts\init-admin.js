/**
 * This script creates an initial admin user in the database.
 * Run it with: node src/scripts/init-admin.js
 * 
 * Make sure you have set the following environment variables in .env.local:
 * - MONGODB_URI
 * - ADMIN_EMAIL
 * - ADMIN_PASSWORD
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { config } = require('dotenv');
const path = require('path');

// Load environment variables from .env.local
config({ path: path.resolve(process.cwd(), '.env.local') });

// Define User schema
const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
  },
  role: {
    type: String,
    enum: ['admin', 'editor'],
    default: 'admin',
  },
}, {
  timestamps: true,
});

// Check for required environment variables
if (!process.env.MONGODB_URI) {
  console.error('MONGODB_URI is not defined in .env.local');
  process.exit(1);
}

if (!process.env.ADMIN_EMAIL) {
  console.error('ADMIN_EMAIL is not defined in .env.local');
  process.exit(1);
}

if (!process.env.ADMIN_PASSWORD) {
  console.error('ADMIN_PASSWORD is not defined in .env.local');
  process.exit(1);
}

async function createAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Create User model
    const User = mongoose.models.User || mongoose.model('User', UserSchema);

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: process.env.ADMIN_EMAIL });
    
    if (existingAdmin) {
      console.log(`Admin user with email ${process.env.ADMIN_EMAIL} already exists`);
      await mongoose.disconnect();
      return;
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD, salt);

    // Create admin user
    const adminUser = new User({
      name: 'Admin',
      email: process.env.ADMIN_EMAIL,
      password: hashedPassword,
      role: 'admin',
    });

    await adminUser.save();
    console.log(`Admin user created with email: ${process.env.ADMIN_EMAIL}`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdminUser();
