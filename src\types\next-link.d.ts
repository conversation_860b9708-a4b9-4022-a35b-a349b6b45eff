declare module 'next/link' {
  import { ComponentProps, ReactNode } from 'react';

  export interface LinkProps {
    href: string;
    as?: string;
    replace?: boolean;
    scroll?: boolean;
    shallow?: boolean;
    passHref?: boolean;
    prefetch?: boolean;
    className?: string;
    children?: ReactNode;
    target?: string;
    rel?: string;
    onClick?: () => void;
  }

  export default function Link(props: LinkProps): JSX.Element;
}
