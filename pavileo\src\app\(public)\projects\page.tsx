'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import OptimizedImage from '@/components/ui/OptimizedImage';

const projects = [
  {
    id: '1',
    title: 'Modern Villa',
    description: 'Luxury villa with contemporary design and premium finishes.',
    category: 'Residential',
    location: 'Varanasi',
    completionDate: 'January 2023',
    client: 'Private Owner',
    images: ['https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: true,
  },
  {
    id: '2',
    title: 'Commercial Complex',
    description: 'Multi-story commercial building with modern amenities.',
    category: 'Commercial',
    location: 'Varanasi',
    completionDate: 'November 2022',
    client: 'ABC Enterprises',
    images: ['https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: true,
  },
  {
    id: '3',
    title: 'Apartment Building',
    description: 'Residential apartment complex with 24 luxury units.',
    category: 'Residential',
    location: 'Varanasi',
    completionDate: 'August 2022',
    client: 'XYZ Developers',
    images: ['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: true,
  },
  {
    id: '4',
    title: 'Office Renovation',
    description: 'Complete renovation of a corporate office space.',
    category: 'Commercial',
    location: 'Varanasi',
    completionDate: 'June 2022',
    client: 'Tech Solutions Inc.',
    images: ['https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '5',
    title: 'Luxury Bungalow',
    description: 'Custom-built luxury bungalow with swimming pool and garden.',
    category: 'Residential',
    location: 'Varanasi',
    completionDate: 'April 2022',
    client: 'Private Owner',
    images: ['https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '6',
    title: 'Shopping Mall',
    description: 'Modern shopping mall with retail spaces, food court, and entertainment zone.',
    category: 'Commercial',
    location: 'Varanasi',
    completionDate: 'February 2022',
    client: 'Retail Ventures Ltd.',
    images: ['https://images.unsplash.com/photo-1519567241046-7f570eee3ce6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '7',
    title: 'Hospital Building',
    description: 'State-of-the-art hospital facility with modern medical infrastructure.',
    category: 'Healthcare',
    location: 'Varanasi',
    completionDate: 'December 2021',
    client: 'Healthcare Group',
    images: ['https://images.unsplash.com/photo-1586773860418-d37222d8fce3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '8',
    title: 'School Campus',
    description: 'Educational campus with classrooms, laboratories, and sports facilities.',
    category: 'Educational',
    location: 'Varanasi',
    completionDate: 'October 2021',
    client: 'Education Trust',
    images: ['https://images.unsplash.com/photo-**********-38a6fa229b23?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '9',
    title: 'Residential Complex',
    description: 'Gated community with 50 residential units and amenities.',
    category: 'Residential',
    location: 'Varanasi',
    completionDate: 'August 2021',
    client: 'Housing Development Corp.',
    images: ['https://images.unsplash.com/photo-1460317442991-0ec209397118?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '10',
    title: 'Medical Center',
    description: 'Specialized medical center with advanced diagnostic facilities.',
    category: 'Healthcare',
    location: 'Varanasi',
    completionDate: 'June 2021',
    client: 'Medical Associates',
    images: ['https://images.unsplash.com/photo-**********-a9333d879b1f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '11',
    title: 'University Library',
    description: 'Modern library building with study halls and digital resources.',
    category: 'Educational',
    location: 'Varanasi',
    completionDate: 'April 2021',
    client: 'State University',
    images: ['https://images.unsplash.com/photo-**********-701939374585?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '12',
    title: 'Wellness Hospital',
    description: 'Comprehensive healthcare facility with rehabilitation center.',
    category: 'Healthcare',
    location: 'Varanasi',
    completionDate: 'February 2021',
    client: 'Wellness Group',
    images: ['https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
  {
    id: '13',
    title: 'Primary School',
    description: 'Child-friendly school building with playground and activity areas.',
    category: 'Educational',
    location: 'Varanasi',
    completionDate: 'December 2020',
    client: 'Education Board',
    images: ['https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'],
    featured: false,
  },
];

const categories = [
  'All',
  'Residential',
  'Commercial',
  'Healthcare',
  'Educational',
];

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredProjects, setFilteredProjects] = useState(projects);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('newest');

  // Refs for GSAP animations
  const heroRef = useRef<HTMLElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  // Filter and sort projects
  useEffect(() => {
    setLoading(true);

    let filtered = projects;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort projects
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.completionDate).getTime() - new Date(a.completionDate).getTime();
        case 'oldest':
          return new Date(a.completionDate).getTime() - new Date(b.completionDate).getTime();
        case 'alphabetical':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    // Add a small delay to show loading state
    setTimeout(() => {
      setFilteredProjects(filtered);
      setLoading(false);
    }, 300);
  }, [selectedCategory, searchTerm, sortBy]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  // GSAP animations
  useEffect(() => {
    if (typeof window !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }

    // Animate hero section
    if (heroRef.current) {
      gsap.fromTo(heroRef.current.children,
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: "power2.out",
          scrollTrigger: {
            trigger: heroRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );
    }

    // Animate filter buttons
    if (filtersRef.current) {
      gsap.fromTo(filtersRef.current.children,
        { y: 20, opacity: 0, scale: 0.8 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.5,
          stagger: 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: filtersRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Add hover effects to filter buttons
      const buttons = filtersRef.current.querySelectorAll('.filter-button');
      buttons.forEach((button) => {
        const buttonElement = button as HTMLElement;

        buttonElement.addEventListener('mouseenter', () => {
          gsap.to(buttonElement, {
            scale: 1.05,
            duration: 0.2,
            ease: "power2.out"
          });
        });

        buttonElement.addEventListener('mouseleave', () => {
          gsap.to(buttonElement, {
            scale: 1,
            duration: 0.2,
            ease: "power2.out"
          });
        });
      });
    }
  }, []);

  // Animate project cards when they change
  useEffect(() => {
    if (gridRef.current && !loading) {
      const cards = gridRef.current.querySelectorAll('.project-card');

      gsap.fromTo(cards,
        { y: 50, opacity: 0, scale: 0.8 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: gridRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Add hover effects to cards
      cards.forEach((card) => {
        const cardElement = card as HTMLElement;

        cardElement.addEventListener('mouseenter', () => {
          gsap.to(cardElement, {
            y: -10,
            scale: 1.02,
            boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
            duration: 0.3,
            ease: "power2.out"
          });
        });

        cardElement.addEventListener('mouseleave', () => {
          gsap.to(cardElement, {
            y: 0,
            scale: 1,
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
            duration: 0.3,
            ease: "power2.out"
          });
        });
      });
    }
  }, [filteredProjects, loading]);

  return (
    <main className="pt-20">
      {/* Hero Section */}
      <section ref={heroRef} className="bg-gray-100 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Our <span className="text-amber-600">Projects</span>
            </h1>
            <p className="text-lg text-gray-700 mb-8">
              Explore our portfolio of completed construction projects that showcase our expertise, quality workmanship, and commitment to excellence.
            </p>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          {/* Search and Sort */}
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>
            <div className="md:w-48">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="alphabetical">A-Z</option>
              </select>
            </div>
          </div>

          {/* Category Filter */}
          <div ref={filtersRef} className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryChange(category)}
                className={`filter-button px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                  category === selectedCategory
                    ? 'bg-amber-600 text-white shadow-lg'
                    : 'bg-gray-200 text-gray-800 hover:bg-gray-300 hover:shadow-md'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Project Count */}
          <div className="text-center mb-8">
            <p className="text-gray-600">
              Showing {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
              {selectedCategory !== 'All' && ` in ${selectedCategory}`}
            </p>
          </div>

          {/* Projects Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg overflow-hidden shadow-md">
                  <div className="h-64 bg-gray-200 animate-pulse"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse mb-4 w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredProjects.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-gray-500 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No projects found</h3>
              <p className="text-gray-500">No projects match the selected category.</p>
            </div>
          ) : (
            <div ref={gridRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredProjects.map((project, index) => (
              <Link key={project.id} href={`/projects/${project.id}`} className="block">
                <div className="project-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 group">
                  <div className="relative h-64 overflow-hidden">
                    <OptimizedImage
                      src={project.images[0]}
                      alt={project.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-110"
                    />

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4">
                      <span className="bg-amber-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                        {project.category}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3 group-hover:text-amber-600 transition-colors duration-300">
                      {project.title}
                    </h3>

                    <p className="text-gray-700 mb-4 line-clamp-2">{project.description}</p>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center text-gray-600 text-sm">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        {project.location}
                      </div>

                      <span className="text-amber-600 font-medium flex items-center group-hover:text-amber-700 transition-colors">
                        View Details
                        <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-amber-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Have a Project in Mind?
            </h2>
            <p className="text-lg text-gray-700 mb-8">
              Let's discuss how we can bring your vision to life. Contact us today for a free consultation.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700"
            >
              Get in Touch
            </a>
          </div>
        </div>
      </section>
    </main>
  );
}
