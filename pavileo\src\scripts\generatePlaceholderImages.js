/**
 * <PERSON><PERSON>t to generate placeholder images for the portfolio
 * This creates simple SVG placeholders that can be used as fallbacks
 */

const fs = require('fs');
const path = require('path');

// Ensure public/images directory exists
const imagesDir = path.join(process.cwd(), 'public', 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Generate SVG logo
const logoSvg = `
<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="40" height="40" rx="8" fill="#D97706"/>
  <path d="M12 28V16L20 12L28 16V28H24V20H16V28H12Z" fill="white"/>
  <circle cx="20" cy="16" r="2" fill="white"/>
</svg>
`;

// Generate white version of logo
const logoWhiteSvg = `
<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="40" height="40" rx="8" fill="white"/>
  <path d="M12 28V16L20 12L28 16V28H24V20H16V28H12Z" fill="#D97706"/>
  <circle cx="20" cy="16" r="2" fill="#D97706"/>
</svg>
`;

// Generate placeholder image SVG
const generatePlaceholderSvg = (width, height, text, bgColor = '#f3f4f6', textColor = '#6b7280') => `
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="${width}" height="${height}" fill="${bgColor}"/>
  <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" fill="${textColor}" font-family="Arial, sans-serif" font-size="14" font-weight="500">${text}</text>
</svg>
`;

// Generate OG image placeholder
const ogImageSvg = `
<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="630" fill="#D97706"/>
  <rect x="100" y="100" width="1000" height="430" fill="white" rx="20"/>
  <text x="600" y="280" text-anchor="middle" dominant-baseline="middle" fill="#D97706" font-family="Arial, sans-serif" font-size="48" font-weight="bold">Pavileo</text>
  <text x="600" y="340" text-anchor="middle" dominant-baseline="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="24">Building Excellence in Varanasi</text>
  <text x="600" y="380" text-anchor="middle" dominant-baseline="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="18">Premium Construction Services</text>
</svg>
`;

// Create favicon SVG
const faviconSvg = `
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" rx="6" fill="#D97706"/>
  <path d="M9 22V13L16 10L23 13V22H20V16H12V22H9Z" fill="white"/>
  <circle cx="16" cy="13" r="1.5" fill="white"/>
</svg>
`;

// File mappings
const files = [
  { name: 'logo.svg', content: logoSvg },
  { name: 'logo-white.svg', content: logoWhiteSvg },
  { name: 'og-image.svg', content: ogImageSvg },
  { name: 'favicon.svg', content: faviconSvg },
  { name: 'placeholder-hero.svg', content: generatePlaceholderSvg(1200, 600, 'Hero Image', '#1f2937', '#f9fafb') },
  { name: 'placeholder-about.svg', content: generatePlaceholderSvg(800, 600, 'About Image', '#f3f4f6', '#6b7280') },
  { name: 'placeholder-service.svg', content: generatePlaceholderSvg(400, 300, 'Service Image', '#fef3c7', '#d97706') },
  { name: 'placeholder-project.svg', content: generatePlaceholderSvg(600, 400, 'Project Image', '#e5e7eb', '#374151') },
  { name: 'placeholder-team.svg', content: generatePlaceholderSvg(300, 300, 'Team Member', '#f9fafb', '#4b5563') },
];

// Generate all files
files.forEach(file => {
  const filePath = path.join(imagesDir, file.name);
  fs.writeFileSync(filePath, file.content);
  console.log(`✅ Generated: ${file.name}`);
});

// Generate some additional placeholder images for different categories
const categories = [
  { name: 'residential', color: '#10b981', text: 'Residential' },
  { name: 'commercial', color: '#3b82f6', text: 'Commercial' },
  { name: 'interior', color: '#8b5cf6', text: 'Interior' },
  { name: 'construction', color: '#f59e0b', text: 'Construction' },
];

categories.forEach(category => {
  const placeholderSvg = generatePlaceholderSvg(600, 400, category.text, category.color, 'white');
  const filePath = path.join(imagesDir, `placeholder-${category.name}.svg`);
  fs.writeFileSync(filePath, placeholderSvg);
  console.log(`✅ Generated: placeholder-${category.name}.svg`);
});

// Create a README file explaining the images
const readmeContent = `# Generated Images

This directory contains automatically generated placeholder images for the Pavileo portfolio.

## Files:
- \`logo.svg\` - Main logo (amber background)
- \`logo-white.svg\` - White background logo
- \`og-image.svg\` - Open Graph image for social sharing
- \`favicon.svg\` - Favicon
- \`placeholder-*.svg\` - Various placeholder images for different sections

## Usage:
These are fallback images that will be used when:
1. External images fail to load
2. No image is provided for a component
3. During development/testing

## Customization:
To replace these with actual images:
1. Replace the SVG files with PNG/JPG versions
2. Update the \`brandingImages\` object in \`src/utils/imageUtils.ts\`
3. Ensure the file names match the expected paths

Generated on: ${new Date().toISOString()}
`;

fs.writeFileSync(path.join(imagesDir, 'README.md'), readmeContent);
console.log('✅ Generated: README.md');

console.log('\n🎉 All placeholder images generated successfully!');
console.log(`📁 Images saved to: ${imagesDir}`);
console.log('\n💡 To use actual images, replace the SVG files with your own images.');
