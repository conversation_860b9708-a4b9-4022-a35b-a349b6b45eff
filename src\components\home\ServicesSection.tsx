'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaHome, FaBuilding, FaPaintRoller, FaRulerCombined, FaClipboardCheck } from 'react-icons/fa';
import Button from '@/components/ui/Button';

interface Service {
  _id: string;
  title: string;
  description: string;
  icon: string;
}

const defaultServices = [
  {
    _id: '1',
    title: 'Residential Construction',
    description: 'Custom homes built to your specifications with quality materials and expert craftsmanship.',
    icon: 'home',
  },
  {
    _id: '2',
    title: 'Commercial Construction',
    description: 'Functional and attractive commercial spaces designed to meet your business needs.',
    icon: 'building',
  },
  {
    _id: '3',
    title: 'Renovation & Remodeling',
    description: 'Transform your existing space with our comprehensive renovation services.',
    icon: 'paintRoller',
  },
  {
    _id: '4',
    title: 'Interior Design',
    description: 'Professional interior design services to create beautiful and functional spaces.',
    icon: 'rulerCombined',
  },
  {
    _id: '5',
    title: 'Project Management',
    description: 'End-to-end project management ensuring timely completion within budget.',
    icon: 'clipboardCheck',
  },
];

const iconMap: Record<string, React.ReactNode> = {
  home: <FaHome size={40} className="text-amber-600" />,
  building: <FaBuilding size={40} className="text-amber-600" />,
  paintRoller: <FaPaintRoller size={40} className="text-amber-600" />,
  rulerCombined: <FaRulerCombined size={40} className="text-amber-600" />,
  clipboardCheck: <FaClipboardCheck size={40} className="text-amber-600" />,
};

const ServicesSection = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await fetch('/api/services');
        if (response.ok) {
          const data = await response.json();
          setServices(data);
        } else {
          // If API fails, use default services
          setServices(defaultServices);
        }
      } catch (error) {
        console.error('Error fetching services:', error);
        setServices(defaultServices);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Use default services if API call is still loading
  const displayServices = loading ? defaultServices : services.length > 0 ? services : defaultServices;

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Our <span className="text-amber-600">Services</span>
          </motion.h2>
          
          <motion.p 
            className="text-gray-700"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            We offer a comprehensive range of construction services to meet all your building needs.
          </motion.p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayServices.map((service, index) => (
            <motion.div
              key={service._id}
              className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="mb-6">
                {iconMap[service.icon] || <FaHome size={40} className="text-amber-600" />}
              </div>
              
              <h3 className="text-xl font-bold mb-4">{service.title}</h3>
              <p className="text-gray-700 mb-6">{service.description}</p>
              
              <Button href="/services" variant="outline">
                Learn More
              </Button>
            </motion.div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <Button href="/services" size="lg">
            View All Services
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
