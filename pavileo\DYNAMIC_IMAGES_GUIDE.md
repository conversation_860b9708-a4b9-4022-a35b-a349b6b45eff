# Dynamic Images System - Pavileo Portfolio

This guide explains how the dynamic image system works in your Pavileo portfolio and how to add/manage images.

## 🎯 Overview

The dynamic image system automatically provides appropriate images for your portfolio components, with smart fallbacks and category-based image selection.

## 📁 File Structure

```
pavileo/
├── public/images/                    # Local placeholder images
│   ├── logo.svg                     # Main logo
│   ├── logo-white.svg               # White variant logo
│   ├── placeholder-*.svg            # Category-specific placeholders
│   └── README.md                    # Generated images documentation
├── src/utils/imageUtils.ts          # Image utility functions
├── src/components/ui/OptimizedImage.tsx  # Enhanced image component
└── src/scripts/generatePlaceholderImages.js  # Image generation script
```

## 🚀 Quick Start

### 1. Generate Placeholder Images
```bash
cd pavileo
node src/scripts/generatePlaceholderImages.js
```

### 2. Use OptimizedImage Component
```tsx
import OptimizedImage from '@/components/ui/OptimizedImage';

<OptimizedImage
  src={imageUrl}
  alt="Description"
  fill
  category="residential"  // Auto-selects appropriate fallback
  className="object-cover"
/>
```

## 🎨 Image Categories

The system supports these categories with automatic fallbacks:

- **`hero`** - Hero/banner images
- **`residential`** - Residential construction projects
- **`commercial`** - Commercial buildings
- **`interior`** - Interior design work
- **`construction`** - Construction process images
- **`team`** - Team member photos
- **`architecture`** - Architectural drawings/plans
- **`tools`** - Construction tools/equipment
- **`default`** - General fallback

## 🔧 Key Functions

### `getImageWithFallback(src, category)`
Returns the provided image or a category-appropriate fallback.

```tsx
import { getImageWithFallback } from '@/utils/imageUtils';

const imageUrl = getImageWithFallback(project.image, 'residential');
```

### `generateImageGallery(id, category, count)`
Creates consistent image galleries for services/projects.

```tsx
const gallery = generateImageGallery('service-1', 'commercial', 3);
```

### `getServiceImage(serviceType)`
Gets appropriate image based on service type.

```tsx
const serviceImage = getServiceImage('residential-construction');
```

## 📱 Components with Dynamic Images

### 1. **OptimizedImage** (Enhanced)
- Automatic fallbacks
- Loading states
- Error handling
- Category-based selection

### 2. **Logo Component**
- SVG-based logo with text fallback
- Multiple variants (default/white)
- Different sizes (sm/md/lg)

### 3. **TeamSection**
- Dynamic team member images
- Professional placeholder generation

### 4. **CompanyGallerySection**
- Office, awards, certificates images
- Category filtering
- Hover effects

## 🎯 Adding Your Own Images

### Method 1: Replace Placeholder Files
1. Add your images to `public/images/`
2. Use the same filenames as placeholders
3. Update `brandingImages` in `imageUtils.ts`

### Method 2: Update Image URLs
1. Upload images to your preferred hosting
2. Update the `imageCategories` object in `imageUtils.ts`
3. Add new URLs to appropriate categories

### Method 3: Database Integration
1. Store image URLs in your database
2. Fetch them in components
3. Use `getImageWithFallback()` for safety

## 🔄 Fallback Strategy

The system uses a multi-level fallback approach:

1. **Primary**: Provided image URL
2. **Secondary**: Category-specific external image
3. **Tertiary**: Local placeholder SVG
4. **Final**: Error state with icon

## 🎨 Customizing Placeholders

### Generate New Placeholders
```javascript
// Edit src/scripts/generatePlaceholderImages.js
const customPlaceholder = generatePlaceholderSvg(
  800, 600,           // dimensions
  'Custom Text',      // text
  '#your-color',      // background
  '#text-color'       // text color
);
```

### Update Categories
```typescript
// In src/utils/imageUtils.ts
export const imageCategories = {
  // Add new category
  newCategory: [
    'https://your-image-url.com/image1.jpg',
    'https://your-image-url.com/image2.jpg',
  ],
  // ... existing categories
};
```

## 🌐 External Image Sources

Current external images are from Unsplash with construction/architecture themes:
- High-quality professional photos
- Consistent styling and color palette
- Optimized for web performance

## 📊 Performance Features

- **Lazy loading** with Next.js Image component
- **Automatic optimization** and resizing
- **WebP conversion** when supported
- **Placeholder blur** during loading
- **Error boundaries** for failed loads

## 🛠️ Development Tips

### Environment Variables
```env
# Use local images in development
NEXT_PUBLIC_USE_LOCAL_IMAGES=true
```

### Testing Images
```tsx
// Force local placeholders for testing
import { getLocalPlaceholder } from '@/utils/imageUtils';
const testImage = getLocalPlaceholder('residential');
```

### Debug Mode
```tsx
// Log image selection process
console.log('Selected image:', getImageWithFallback(src, category));
```

## 🎯 Best Practices

1. **Always specify category** for better fallbacks
2. **Use descriptive alt text** for accessibility
3. **Optimize images** before uploading (WebP, appropriate sizes)
4. **Test fallback scenarios** by temporarily breaking image URLs
5. **Monitor loading performance** with browser dev tools

## 🔧 Troubleshooting

### Images Not Loading
1. Check browser console for errors
2. Verify image URLs are accessible
3. Ensure Next.js `remotePatterns` includes your domains
4. Test with local placeholders

### Placeholder Generation Issues
1. Ensure Node.js can write to `public/images/`
2. Check file permissions
3. Verify SVG syntax in generated files

### Performance Issues
1. Use appropriate image sizes
2. Enable Next.js image optimization
3. Consider CDN for external images
4. Monitor Core Web Vitals

## 📈 Future Enhancements

- **Image compression** pipeline
- **Multiple format support** (AVIF, WebP, JPEG)
- **Responsive image sets** for different screen sizes
- **Admin panel** for image management
- **Automatic alt text** generation
- **Image analytics** and usage tracking

## 🎉 Ready to Use!

Your portfolio now has a robust, dynamic image system that:
- ✅ Automatically provides appropriate images
- ✅ Handles errors gracefully
- ✅ Supports easy customization
- ✅ Optimizes performance
- ✅ Maintains visual consistency

Start by running the placeholder generation script and then customize with your own images!
