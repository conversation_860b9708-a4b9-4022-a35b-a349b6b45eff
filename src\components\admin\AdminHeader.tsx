'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import { FaBars, FaTimes, FaUser, FaSignOutAlt } from 'react-icons/fa';

const AdminHeader = () => {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const { data: session } = useSession();

  const toggleMenu = () => setIsOpen(!isOpen);

  const navLinks = [
    { href: '/admin', label: 'Dashboard' },
    { href: '/admin/projects', label: 'Projects' },
    { href: '/admin/services', label: 'Services' },
    { href: '/admin/testimonials', label: 'Testimonials' },
    { href: '/admin/messages', label: 'Messages' },
    { href: '/admin/categories', label: 'Categories' },
    { href: '/admin/sections', label: 'Sections' },
  ];

  return (
    <header className="bg-gray-800 text-white">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link href="/admin" className="flex items-center">
            <span className="text-xl font-bold text-amber-500">Pavileo Admin</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`text-sm font-medium transition-colors hover:text-amber-400 ${
                  pathname === link.href ? 'text-amber-400' : 'text-gray-300'
                }`}
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="flex items-center">
              <FaUser className="text-gray-400 mr-2" />
              <span className="text-sm font-medium">{session?.user?.name || 'Admin'}</span>
            </div>
            <button
              onClick={() => signOut({ callbackUrl: '/admin/login' })}
              className="flex items-center text-sm font-medium text-gray-300 hover:text-amber-400 transition-colors"
            >
              <FaSignOutAlt className="mr-2" />
              Sign Out
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-gray-300 focus:outline-none"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-gray-700">
            <nav className="flex flex-col space-y-4 mb-4">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`text-sm font-medium py-2 transition-colors hover:text-amber-400 ${
                    pathname === link.href ? 'text-amber-400' : 'text-gray-300'
                  }`}
                  onClick={() => setIsOpen(false)}
                >
                  {link.label}
                </Link>
              ))}
            </nav>
            <div className="flex flex-col space-y-4 pt-4 border-t border-gray-700">
              <div className="flex items-center">
                <FaUser className="text-gray-400 mr-2" />
                <span className="text-sm font-medium">{session?.user?.name || 'Admin'}</span>
              </div>
              <button
                onClick={() => signOut({ callbackUrl: '/admin/login' })}
                className="flex items-center text-sm font-medium text-gray-300 hover:text-amber-400 transition-colors"
              >
                <FaSignOutAlt className="mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default AdminHeader;
