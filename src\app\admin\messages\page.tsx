'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaEye, FaTrash, FaEnvelope, FaEnvelopeOpen } from 'react-icons/fa';

interface Message {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  createdAt: string;
  read: boolean;
}

export default function AdminMessages() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        // In a real application, you would fetch this data from your API
        // For now, we'll use placeholder data
        setMessages([
          {
            id: '1',
            name: '<PERSON><PERSON><PERSON>',
            email: '<EMAIL>',
            subject: 'New Home Construction Inquiry',
            message: 'I am interested in building a new home in Varanasi. I would like to schedule a consultation to discuss my requirements and get a quote.',
            createdAt: '2023-05-01T10:30:00Z',
            read: false,
          },
          {
            id: '2',
            name: '<PERSON><PERSON>',
            email: '<EMAIL>',
            subject: 'Office Renovation Project',
            message: 'We are looking to renovate our office space in Varanasi. Please provide information about your commercial renovation services and availability.',
            createdAt: '2023-04-28T14:15:00Z',
            read: true,
          },
          {
            id: '3',
            name: 'Rahul Patel',
            email: '<EMAIL>',
            subject: 'Interior Design Services',
            message: 'I recently purchased a new apartment and need interior design services. I would like to know more about your packages and pricing.',
            createdAt: '2023-04-25T09:45:00Z',
            read: true,
          },
        ]);
      } catch (error) {
        console.error('Error fetching messages:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, []);

  const handleDelete = (id: string) => {
    // In a real application, you would call your API to delete the message
    setMessages(messages.filter(message => message.id !== id));
  };

  const markAsRead = (id: string) => {
    // In a real application, you would call your API to mark the message as read
    setMessages(messages.map(message => 
      message.id === id ? { ...message, read: true } : message
    ));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Messages</h1>
        <div className="text-sm text-gray-600">
          {messages.filter(m => !m.read).length} unread messages
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {messages.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No messages to display
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    From
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subject
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {messages.map((message) => (
                  <tr 
                    key={message.id} 
                    className={message.read ? 'bg-white' : 'bg-amber-50'}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      {message.read ? (
                        <FaEnvelopeOpen className="text-gray-400" />
                      ) : (
                        <FaEnvelope className="text-amber-600" />
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{message.name}</div>
                      <div className="text-sm text-gray-500">{message.email}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 font-medium">{message.subject}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">{message.message}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{formatDate(message.createdAt)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link 
                          href={`/admin/messages/${message.id}`}
                          className="text-amber-600 hover:text-amber-900"
                          onClick={() => !message.read && markAsRead(message.id)}
                        >
                          <FaEye />
                        </Link>
                        <button 
                          onClick={() => handleDelete(message.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      )}
    </div>
  );
}
