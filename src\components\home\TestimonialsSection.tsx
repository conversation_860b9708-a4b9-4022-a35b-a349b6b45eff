'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { FaStar } from 'react-icons/fa';

interface Testimonial {
  _id: string;
  name: string;
  position: string;
  content: string;
  rating: number;
  image?: string;
}

const defaultTestimonials = [
  {
    _id: '1',
    name: '<PERSON><PERSON>',
    position: 'Homeowner',
    content: '<PERSON><PERSON><PERSON> transformed our dream home into reality. Their attention to detail and quality of work exceeded our expectations. Highly recommended!',
    rating: 5,
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    _id: '2',
    name: '<PERSON><PERSON>',
    position: 'Business Owner',
    content: 'We hired <PERSON><PERSON><PERSON> for our office renovation and they delivered exceptional results. Professional team, on-time completion, and excellent craftsmanship.',
    rating: 5,
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
  },
  {
    _id: '3',
    name: '<PERSON><PERSON>',
    position: 'Property Developer',
    content: 'Working with <PERSON><PERSON><PERSON> on our apartment complex was a great experience. Their expertise and project management skills are outstanding.',
    rating: 4,
    image: 'https://randomuser.me/api/portraits/men/68.jpg',
  },
];

const TestimonialsSection = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/testimonials');
        if (response.ok) {
          const data = await response.json();
          setTestimonials(data);
        } else {
          // If API fails, use default testimonials
          setTestimonials(defaultTestimonials);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        setTestimonials(defaultTestimonials);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  // Use default testimonials if API call is still loading
  const displayTestimonials = loading ? defaultTestimonials : testimonials.length > 0 ? testimonials : defaultTestimonials;

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % displayTestimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [displayTestimonials.length]);

  return (
    <section className="py-16 md:py-24 bg-amber-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            What Our <span className="text-amber-600">Clients Say</span>
          </motion.h2>

          <motion.p
            className="text-gray-700"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Hear from our satisfied clients about their experience working with Pavileo.
          </motion.p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {displayTestimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial._id}
                className={`bg-white p-8 md:p-10 rounded-lg shadow-md ${
                  index === activeIndex ? 'block' : 'hidden'
                }`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <div className="flex-shrink-0">
                    <div className="relative w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden">
                      <Image
                        src={testimonial.image || 'https://randomuser.me/api/portraits/lego/1.jpg'}
                        alt={testimonial.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>

                  <div className="flex-1 text-center md:text-left">
                    <div className="flex justify-center md:justify-start mb-4">
                      {[...Array(5)].map((_, i) => (
                        <FaStar
                          key={i}
                          className={i < testimonial.rating ? 'text-amber-500' : 'text-gray-300'}
                        />
                      ))}
                    </div>

                    <p className="text-gray-700 mb-6 italic">"{testimonial.content}"</p>

                    <div>
                      <h4 className="text-lg font-bold">{testimonial.name}</h4>
                      <p className="text-gray-600">{testimonial.position}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="flex justify-center mt-8 space-x-2">
            {displayTestimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === activeIndex ? 'bg-amber-600' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
