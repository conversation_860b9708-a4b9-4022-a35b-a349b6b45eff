import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Mock data for demo purposes
const mockTestimonials = [
  {
    _id: '1',
    name: '<PERSON><PERSON>',
    position: 'Homeowner',
    content: '<PERSON><PERSON><PERSON> transformed our dream home into reality. Their attention to detail and quality of work exceeded our expectations. Highly recommended!',
    rating: 5,
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    _id: '2',
    name: '<PERSON><PERSON>',
    position: 'Business Owner',
    content: 'We hired <PERSON><PERSON><PERSON> for our office renovation and they delivered exceptional results. Professional team, on-time completion, and excellent craftsmanship.',
    rating: 5,
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
  },
  {
    _id: '3',
    name: '<PERSON><PERSON>',
    position: 'Property Developer',
    content: 'Working with <PERSON><PERSON><PERSON> on our apartment complex was a great experience. Their expertise and project management skills are outstanding.',
    rating: 4,
    image: 'https://randomuser.me/api/portraits/men/68.jpg',
  },
];

export async function GET(req: NextRequest) {
  try {
    return NextResponse.json(mockTestimonials);
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    return NextResponse.json(
      { error: 'Failed to fetch testimonials' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // In a real app, you would check authentication and save to database
    return NextResponse.json(
      { message: 'Testimonial created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating testimonial:', error);
    return NextResponse.json(
      { error: 'Failed to create testimonial' },
      { status: 500 }
    );
  }
}
