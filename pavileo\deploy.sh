#!/bin/bash

# Pavileo Construction Website Deployment Script
# This script helps deploy the Pavileo website to Vercel

echo "🚀 Starting deployment process for Pavileo Construction Website..."

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed. Installing now..."
    npm install -g vercel
fi

# Run linting
echo "🔍 Running linting checks..."
npm run lint

# Run build to check for errors
echo "🏗️ Building project to check for errors..."
npm run build

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors before deploying."
    exit 1
fi

echo "✅ Build successful!"

# Ask for deployment type
echo "📊 Select deployment type:"
echo "1) Preview deployment (for testing)"
echo "2) Production deployment"
read -p "Enter your choice (1/2): " deployment_choice

# Deploy based on choice
if [ "$deployment_choice" = "1" ]; then
    echo "🔄 Deploying to preview environment..."
    vercel
elif [ "$deployment_choice" = "2" ]; then
    echo "🔄 Deploying to production environment..."
    vercel --prod
else
    echo "❌ Invalid choice. Exiting."
    exit 1
fi

echo "✅ Deployment completed!"
echo "🌐 Your website is now live!"
echo "📝 Remember to update your environment variables in the Vercel dashboard if needed."
