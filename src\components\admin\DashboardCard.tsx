import React from 'react';
import Link from 'next/link';
import { IconType } from 'react-icons';

interface DashboardCardProps {
  title: string;
  count: number;
  icon: React.ReactNode;
  href: string;
  color: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  count,
  icon,
  href,
  color,
}) => {
  return (
    <Link href={href} className="block">
      <div className={`bg-white rounded-lg shadow-md overflow-hidden border-t-4 ${color} hover:shadow-lg transition-shadow`}>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium mb-1">{title}</p>
              <h3 className="text-3xl font-bold text-gray-800">{count}</h3>
            </div>
            <div className="text-gray-400 text-3xl">{icon}</div>
          </div>
          <div className="mt-4 text-sm font-medium text-gray-600">
            View Details &rarr;
          </div>
        </div>
      </div>
    </Link>
  );
};

export default DashboardCard;
